import { AudioAinsMode, AudioEffectPreset, AudioEncodedFrameObserverConfig, AudioProfileType, AudioRecordingConfiguration, AudioScenarioType, AudioSessionOperationRestriction, BeautyOptions, CameraStabilizationMode, ChannelMediaRelayConfiguration, ChannelProfileType, ClientRoleOptions, ClientRoleType, CodecCapInfo, ColorEnhanceOptions, ConnectionStateType, DataStreamConfig, DeviceInfo, EarMonitoringFilterType, EchoTestConfiguration, EncryptionConfig, FaceShapeArea, FaceShapeAreaOptions, FaceShapeBeautyOptions, FilterEffectOptions, FocalLengthInfo, HdrCapability, HeadphoneEqualizerPreset, IAudioEncodedFrameObserver, LastmileProbeConfig, LiveTranscoding, LocalAccessPointConfiguration, LocalAudioMixerConfiguration, LocalTranscoderConfiguration, LowlightEnhanceOptions, RecorderStreamInfo, Rectangle, ScreenCaptureParameters, ScreenCaptureParameters2, ScreenScenarioType, SegmentationProperty, SenderOptions, SimulcastConfig, SimulcastStreamConfig, SimulcastStreamMode, SpatialAudioParams, UserInfo, VideoApplicationScenarioType, VideoCanvas, VideoContentHint, VideoDenoiserOptions, VideoEncoderConfiguration, VideoFormat, VideoMirrorModeType, VideoModuleType, VideoOrientation, VideoQoePreferenceType, VideoStreamType, VideoSubscriptionOptions, VirtualBackgroundSource, VoiceAiTunerType, VoiceBeautifierPreset, VoiceConversionPreset, WatermarkOptions } from '../AgoraBase';
import { ContentInspectConfig, IAudioSpectrumObserver, MediaSourceType, RawAudioFrameOpModeType, RenderModeType, SnapshotConfig, VideoSourceType } from '../AgoraMediaBase';
import { IH265Transcoder } from '../IAgoraH265Transcoder';
import { LogFilterType, LogLevel } from '../IAgoraLog';
import { AudioMixingDualMonoMode, IMediaEngine } from '../IAgoraMediaEngine';
import { IMediaPlayer } from '../IAgoraMediaPlayer';
import { IMediaRecorder } from '../IAgoraMediaRecorder';
import { IMusicContentCenter } from '../IAgoraMusicContentCenter';
import { AgoraRhythmPlayerConfig } from '../IAgoraRhythmPlayer';
import { AdvancedAudioOptions, AudioEqualizationBandFrequency, AudioReverbType, CameraCapturerConfiguration, ChannelMediaOptions, CloudProxyType, DirectCdnStreamingMediaOptions, FeatureType, IDirectCdnStreamingEventHandler, IMetadataObserver, IRtcEngine, IRtcEngineEventHandler, IVideoDeviceManager, ImageTrackOptions, LeaveChannelOptions, Metadata, MetadataType, PriorityType, RtcEngineContext, SDKBuildInfo, ScreenCaptureConfiguration, ScreenCaptureSourceInfo, Size, StreamFallbackOptions, VideoDeviceInfo } from '../IAgoraRtcEngine';
import { ILocalSpatialAudioEngine } from '../IAgoraSpatialAudio';
import { IAudioDeviceManager } from '../IAudioDeviceManager';
export declare function processIRtcEngineEventHandler(handler: IRtcEngineEventHandler, event: string, jsonParams: any): void;
export declare class IVideoDeviceManagerImpl implements IVideoDeviceManager {
    enumerateVideoDevices(): VideoDeviceInfo[];
    protected getApiTypeFromEnumerateVideoDevices(): string;
    setDevice(deviceIdUTF8: string): number;
    protected getApiTypeFromSetDevice(deviceIdUTF8: string): string;
    getDevice(): string;
    protected getApiTypeFromGetDevice(): string;
    numberOfCapabilities(deviceIdUTF8: string): number;
    protected getApiTypeFromNumberOfCapabilities(deviceIdUTF8: string): string;
    getCapability(deviceIdUTF8: string, deviceCapabilityNumber: number): VideoFormat;
    protected getApiTypeFromGetCapability(deviceIdUTF8: string, deviceCapabilityNumber: number): string;
    startDeviceTest(hwnd: any): number;
    protected getApiTypeFromStartDeviceTest(hwnd: any): string;
    stopDeviceTest(): number;
    protected getApiTypeFromStopDeviceTest(): string;
    release(): void;
    protected getApiTypeFromRelease(): string;
}
export declare function processIMetadataObserver(handler: IMetadataObserver, event: string, jsonParams: any): void;
export declare function processIDirectCdnStreamingEventHandler(handler: IDirectCdnStreamingEventHandler, event: string, jsonParams: any): void;
export declare class IRtcEngineImpl implements IRtcEngine {
    initialize(context: RtcEngineContext): number;
    protected getApiTypeFromInitialize(context: RtcEngineContext): string;
    getVersion(): SDKBuildInfo;
    protected getApiTypeFromGetVersion(): string;
    getErrorDescription(code: number): string;
    protected getApiTypeFromGetErrorDescription(code: number): string;
    queryCodecCapability(): {
        codecInfo: CodecCapInfo[];
        size: number;
    };
    protected getApiTypeFromQueryCodecCapability(): string;
    queryDeviceScore(): number;
    protected getApiTypeFromQueryDeviceScore(): string;
    preloadChannel(token: string, channelId: string, uid: number): number;
    protected getApiTypeFromPreloadChannel(token: string, channelId: string, uid: number): string;
    preloadChannelWithUserAccount(token: string, channelId: string, userAccount: string): number;
    protected getApiTypeFromPreloadChannelWithUserAccount(token: string, channelId: string, userAccount: string): string;
    updatePreloadChannelToken(token: string): number;
    protected getApiTypeFromUpdatePreloadChannelToken(token: string): string;
    joinChannel(token: string, channelId: string, uid: number, options: ChannelMediaOptions): number;
    protected getApiTypeFromJoinChannel(token: string, channelId: string, uid: number, options: ChannelMediaOptions): string;
    updateChannelMediaOptions(options: ChannelMediaOptions): number;
    protected getApiTypeFromUpdateChannelMediaOptions(options: ChannelMediaOptions): string;
    leaveChannel(options?: LeaveChannelOptions): number;
    protected getApiTypeFromLeaveChannel(options?: LeaveChannelOptions): string;
    renewToken(token: string): number;
    protected getApiTypeFromRenewToken(token: string): string;
    setChannelProfile(profile: ChannelProfileType): number;
    protected getApiTypeFromSetChannelProfile(profile: ChannelProfileType): string;
    setClientRole(role: ClientRoleType, options?: ClientRoleOptions): number;
    protected getApiTypeFromSetClientRole(role: ClientRoleType, options?: ClientRoleOptions): string;
    startEchoTest(config: EchoTestConfiguration): number;
    protected getApiTypeFromStartEchoTest(config: EchoTestConfiguration): string;
    stopEchoTest(): number;
    protected getApiTypeFromStopEchoTest(): string;
    enableMultiCamera(enabled: boolean, config: CameraCapturerConfiguration): number;
    protected getApiTypeFromEnableMultiCamera(enabled: boolean, config: CameraCapturerConfiguration): string;
    enableVideo(): number;
    protected getApiTypeFromEnableVideo(): string;
    disableVideo(): number;
    protected getApiTypeFromDisableVideo(): string;
    startPreview(sourceType?: VideoSourceType): number;
    protected getApiTypeFromStartPreview(sourceType?: VideoSourceType): string;
    stopPreview(sourceType?: VideoSourceType): number;
    protected getApiTypeFromStopPreview(sourceType?: VideoSourceType): string;
    startLastmileProbeTest(config: LastmileProbeConfig): number;
    protected getApiTypeFromStartLastmileProbeTest(config: LastmileProbeConfig): string;
    stopLastmileProbeTest(): number;
    protected getApiTypeFromStopLastmileProbeTest(): string;
    setVideoEncoderConfiguration(config: VideoEncoderConfiguration): number;
    protected getApiTypeFromSetVideoEncoderConfiguration(config: VideoEncoderConfiguration): string;
    setBeautyEffectOptions(enabled: boolean, options: BeautyOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetBeautyEffectOptions(enabled: boolean, options: BeautyOptions, type?: MediaSourceType): string;
    setFaceShapeBeautyOptions(enabled: boolean, options: FaceShapeBeautyOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetFaceShapeBeautyOptions(enabled: boolean, options: FaceShapeBeautyOptions, type?: MediaSourceType): string;
    setFaceShapeAreaOptions(options: FaceShapeAreaOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetFaceShapeAreaOptions(options: FaceShapeAreaOptions, type?: MediaSourceType): string;
    getFaceShapeBeautyOptions(type?: MediaSourceType): FaceShapeBeautyOptions;
    protected getApiTypeFromGetFaceShapeBeautyOptions(type?: MediaSourceType): string;
    getFaceShapeAreaOptions(shapeArea: FaceShapeArea, type?: MediaSourceType): FaceShapeAreaOptions;
    protected getApiTypeFromGetFaceShapeAreaOptions(shapeArea: FaceShapeArea, type?: MediaSourceType): string;
    setFilterEffectOptions(enabled: boolean, options: FilterEffectOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetFilterEffectOptions(enabled: boolean, options: FilterEffectOptions, type?: MediaSourceType): string;
    setLowlightEnhanceOptions(enabled: boolean, options: LowlightEnhanceOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetLowlightEnhanceOptions(enabled: boolean, options: LowlightEnhanceOptions, type?: MediaSourceType): string;
    setVideoDenoiserOptions(enabled: boolean, options: VideoDenoiserOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetVideoDenoiserOptions(enabled: boolean, options: VideoDenoiserOptions, type?: MediaSourceType): string;
    setColorEnhanceOptions(enabled: boolean, options: ColorEnhanceOptions, type?: MediaSourceType): number;
    protected getApiTypeFromSetColorEnhanceOptions(enabled: boolean, options: ColorEnhanceOptions, type?: MediaSourceType): string;
    enableVirtualBackground(enabled: boolean, backgroundSource: VirtualBackgroundSource, segproperty: SegmentationProperty, type?: MediaSourceType): number;
    protected getApiTypeFromEnableVirtualBackground(enabled: boolean, backgroundSource: VirtualBackgroundSource, segproperty: SegmentationProperty, type?: MediaSourceType): string;
    setupRemoteVideo(canvas: VideoCanvas): number;
    protected getApiTypeFromSetupRemoteVideo(canvas: VideoCanvas): string;
    setupLocalVideo(canvas: VideoCanvas): number;
    protected getApiTypeFromSetupLocalVideo(canvas: VideoCanvas): string;
    setVideoScenario(scenarioType: VideoApplicationScenarioType): number;
    protected getApiTypeFromSetVideoScenario(scenarioType: VideoApplicationScenarioType): string;
    setVideoQoEPreference(qoePreference: VideoQoePreferenceType): number;
    protected getApiTypeFromSetVideoQoEPreference(qoePreference: VideoQoePreferenceType): string;
    enableAudio(): number;
    protected getApiTypeFromEnableAudio(): string;
    disableAudio(): number;
    protected getApiTypeFromDisableAudio(): string;
    setAudioProfile(profile: AudioProfileType, scenario?: AudioScenarioType): number;
    protected getApiTypeFromSetAudioProfile(profile: AudioProfileType, scenario?: AudioScenarioType): string;
    setAudioScenario(scenario: AudioScenarioType): number;
    protected getApiTypeFromSetAudioScenario(scenario: AudioScenarioType): string;
    enableLocalAudio(enabled: boolean): number;
    protected getApiTypeFromEnableLocalAudio(enabled: boolean): string;
    muteLocalAudioStream(mute: boolean): number;
    protected getApiTypeFromMuteLocalAudioStream(mute: boolean): string;
    muteAllRemoteAudioStreams(mute: boolean): number;
    protected getApiTypeFromMuteAllRemoteAudioStreams(mute: boolean): string;
    muteRemoteAudioStream(uid: number, mute: boolean): number;
    protected getApiTypeFromMuteRemoteAudioStream(uid: number, mute: boolean): string;
    muteLocalVideoStream(mute: boolean): number;
    protected getApiTypeFromMuteLocalVideoStream(mute: boolean): string;
    enableLocalVideo(enabled: boolean): number;
    protected getApiTypeFromEnableLocalVideo(enabled: boolean): string;
    muteAllRemoteVideoStreams(mute: boolean): number;
    protected getApiTypeFromMuteAllRemoteVideoStreams(mute: boolean): string;
    setRemoteDefaultVideoStreamType(streamType: VideoStreamType): number;
    protected getApiTypeFromSetRemoteDefaultVideoStreamType(streamType: VideoStreamType): string;
    muteRemoteVideoStream(uid: number, mute: boolean): number;
    protected getApiTypeFromMuteRemoteVideoStream(uid: number, mute: boolean): string;
    setRemoteVideoStreamType(uid: number, streamType: VideoStreamType): number;
    protected getApiTypeFromSetRemoteVideoStreamType(uid: number, streamType: VideoStreamType): string;
    setRemoteVideoSubscriptionOptions(uid: number, options: VideoSubscriptionOptions): number;
    protected getApiTypeFromSetRemoteVideoSubscriptionOptions(uid: number, options: VideoSubscriptionOptions): string;
    setSubscribeAudioBlocklist(uidList: number[], uidNumber: number): number;
    protected getApiTypeFromSetSubscribeAudioBlocklist(uidList: number[], uidNumber: number): string;
    setSubscribeAudioAllowlist(uidList: number[], uidNumber: number): number;
    protected getApiTypeFromSetSubscribeAudioAllowlist(uidList: number[], uidNumber: number): string;
    setSubscribeVideoBlocklist(uidList: number[], uidNumber: number): number;
    protected getApiTypeFromSetSubscribeVideoBlocklist(uidList: number[], uidNumber: number): string;
    setSubscribeVideoAllowlist(uidList: number[], uidNumber: number): number;
    protected getApiTypeFromSetSubscribeVideoAllowlist(uidList: number[], uidNumber: number): string;
    enableAudioVolumeIndication(interval: number, smooth: number, reportVad: boolean): number;
    protected getApiTypeFromEnableAudioVolumeIndication(interval: number, smooth: number, reportVad: boolean): string;
    startAudioRecording(config: AudioRecordingConfiguration): number;
    protected getApiTypeFromStartAudioRecording(config: AudioRecordingConfiguration): string;
    registerAudioEncodedFrameObserver(config: AudioEncodedFrameObserverConfig, observer: IAudioEncodedFrameObserver): number;
    protected getApiTypeFromRegisterAudioEncodedFrameObserver(config: AudioEncodedFrameObserverConfig, observer: IAudioEncodedFrameObserver): string;
    stopAudioRecording(): number;
    protected getApiTypeFromStopAudioRecording(): string;
    createMediaPlayer(): IMediaPlayer;
    protected getApiTypeFromCreateMediaPlayer(): string;
    destroyMediaPlayer(mediaPlayer: IMediaPlayer): number;
    protected getApiTypeFromDestroyMediaPlayer(mediaPlayer: IMediaPlayer): string;
    createMediaRecorder(info: RecorderStreamInfo): IMediaRecorder;
    protected getApiTypeFromCreateMediaRecorder(info: RecorderStreamInfo): string;
    destroyMediaRecorder(mediaRecorder: IMediaRecorder): number;
    protected getApiTypeFromDestroyMediaRecorder(mediaRecorder: IMediaRecorder): string;
    startAudioMixing(filePath: string, loopback: boolean, cycle: number, startPos?: number): number;
    protected getApiTypeFromStartAudioMixing(filePath: string, loopback: boolean, cycle: number, startPos?: number): string;
    stopAudioMixing(): number;
    protected getApiTypeFromStopAudioMixing(): string;
    pauseAudioMixing(): number;
    protected getApiTypeFromPauseAudioMixing(): string;
    resumeAudioMixing(): number;
    protected getApiTypeFromResumeAudioMixing(): string;
    selectAudioTrack(index: number): number;
    protected getApiTypeFromSelectAudioTrack(index: number): string;
    getAudioTrackCount(): number;
    protected getApiTypeFromGetAudioTrackCount(): string;
    adjustAudioMixingVolume(volume: number): number;
    protected getApiTypeFromAdjustAudioMixingVolume(volume: number): string;
    adjustAudioMixingPublishVolume(volume: number): number;
    protected getApiTypeFromAdjustAudioMixingPublishVolume(volume: number): string;
    getAudioMixingPublishVolume(): number;
    protected getApiTypeFromGetAudioMixingPublishVolume(): string;
    adjustAudioMixingPlayoutVolume(volume: number): number;
    protected getApiTypeFromAdjustAudioMixingPlayoutVolume(volume: number): string;
    getAudioMixingPlayoutVolume(): number;
    protected getApiTypeFromGetAudioMixingPlayoutVolume(): string;
    getAudioMixingDuration(): number;
    protected getApiTypeFromGetAudioMixingDuration(): string;
    getAudioMixingCurrentPosition(): number;
    protected getApiTypeFromGetAudioMixingCurrentPosition(): string;
    setAudioMixingPosition(pos: number): number;
    protected getApiTypeFromSetAudioMixingPosition(pos: number): string;
    setAudioMixingDualMonoMode(mode: AudioMixingDualMonoMode): number;
    protected getApiTypeFromSetAudioMixingDualMonoMode(mode: AudioMixingDualMonoMode): string;
    setAudioMixingPitch(pitch: number): number;
    protected getApiTypeFromSetAudioMixingPitch(pitch: number): string;
    setAudioMixingPlaybackSpeed(speed: number): number;
    protected getApiTypeFromSetAudioMixingPlaybackSpeed(speed: number): string;
    getEffectsVolume(): number;
    protected getApiTypeFromGetEffectsVolume(): string;
    setEffectsVolume(volume: number): number;
    protected getApiTypeFromSetEffectsVolume(volume: number): string;
    preloadEffect(soundId: number, filePath: string, startPos?: number): number;
    protected getApiTypeFromPreloadEffect(soundId: number, filePath: string, startPos?: number): string;
    playEffect(soundId: number, filePath: string, loopCount: number, pitch: number, pan: number, gain: number, publish?: boolean, startPos?: number): number;
    protected getApiTypeFromPlayEffect(soundId: number, filePath: string, loopCount: number, pitch: number, pan: number, gain: number, publish?: boolean, startPos?: number): string;
    playAllEffects(loopCount: number, pitch: number, pan: number, gain: number, publish?: boolean): number;
    protected getApiTypeFromPlayAllEffects(loopCount: number, pitch: number, pan: number, gain: number, publish?: boolean): string;
    getVolumeOfEffect(soundId: number): number;
    protected getApiTypeFromGetVolumeOfEffect(soundId: number): string;
    setVolumeOfEffect(soundId: number, volume: number): number;
    protected getApiTypeFromSetVolumeOfEffect(soundId: number, volume: number): string;
    pauseEffect(soundId: number): number;
    protected getApiTypeFromPauseEffect(soundId: number): string;
    pauseAllEffects(): number;
    protected getApiTypeFromPauseAllEffects(): string;
    resumeEffect(soundId: number): number;
    protected getApiTypeFromResumeEffect(soundId: number): string;
    resumeAllEffects(): number;
    protected getApiTypeFromResumeAllEffects(): string;
    stopEffect(soundId: number): number;
    protected getApiTypeFromStopEffect(soundId: number): string;
    stopAllEffects(): number;
    protected getApiTypeFromStopAllEffects(): string;
    unloadEffect(soundId: number): number;
    protected getApiTypeFromUnloadEffect(soundId: number): string;
    unloadAllEffects(): number;
    protected getApiTypeFromUnloadAllEffects(): string;
    getEffectDuration(filePath: string): number;
    protected getApiTypeFromGetEffectDuration(filePath: string): string;
    setEffectPosition(soundId: number, pos: number): number;
    protected getApiTypeFromSetEffectPosition(soundId: number, pos: number): string;
    getEffectCurrentPosition(soundId: number): number;
    protected getApiTypeFromGetEffectCurrentPosition(soundId: number): string;
    enableSoundPositionIndication(enabled: boolean): number;
    protected getApiTypeFromEnableSoundPositionIndication(enabled: boolean): string;
    setRemoteVoicePosition(uid: number, pan: number, gain: number): number;
    protected getApiTypeFromSetRemoteVoicePosition(uid: number, pan: number, gain: number): string;
    enableSpatialAudio(enabled: boolean): number;
    protected getApiTypeFromEnableSpatialAudio(enabled: boolean): string;
    setRemoteUserSpatialAudioParams(uid: number, params: SpatialAudioParams): number;
    protected getApiTypeFromSetRemoteUserSpatialAudioParams(uid: number, params: SpatialAudioParams): string;
    setVoiceBeautifierPreset(preset: VoiceBeautifierPreset): number;
    protected getApiTypeFromSetVoiceBeautifierPreset(preset: VoiceBeautifierPreset): string;
    setAudioEffectPreset(preset: AudioEffectPreset): number;
    protected getApiTypeFromSetAudioEffectPreset(preset: AudioEffectPreset): string;
    setVoiceConversionPreset(preset: VoiceConversionPreset): number;
    protected getApiTypeFromSetVoiceConversionPreset(preset: VoiceConversionPreset): string;
    setAudioEffectParameters(preset: AudioEffectPreset, param1: number, param2: number): number;
    protected getApiTypeFromSetAudioEffectParameters(preset: AudioEffectPreset, param1: number, param2: number): string;
    setVoiceBeautifierParameters(preset: VoiceBeautifierPreset, param1: number, param2: number): number;
    protected getApiTypeFromSetVoiceBeautifierParameters(preset: VoiceBeautifierPreset, param1: number, param2: number): string;
    setVoiceConversionParameters(preset: VoiceConversionPreset, param1: number, param2: number): number;
    protected getApiTypeFromSetVoiceConversionParameters(preset: VoiceConversionPreset, param1: number, param2: number): string;
    setLocalVoicePitch(pitch: number): number;
    protected getApiTypeFromSetLocalVoicePitch(pitch: number): string;
    setLocalVoiceFormant(formantRatio: number): number;
    protected getApiTypeFromSetLocalVoiceFormant(formantRatio: number): string;
    setLocalVoiceEqualization(bandFrequency: AudioEqualizationBandFrequency, bandGain: number): number;
    protected getApiTypeFromSetLocalVoiceEqualization(bandFrequency: AudioEqualizationBandFrequency, bandGain: number): string;
    setLocalVoiceReverb(reverbKey: AudioReverbType, value: number): number;
    protected getApiTypeFromSetLocalVoiceReverb(reverbKey: AudioReverbType, value: number): string;
    setHeadphoneEQPreset(preset: HeadphoneEqualizerPreset): number;
    protected getApiTypeFromSetHeadphoneEQPreset(preset: HeadphoneEqualizerPreset): string;
    setHeadphoneEQParameters(lowGain: number, highGain: number): number;
    protected getApiTypeFromSetHeadphoneEQParameters(lowGain: number, highGain: number): string;
    enableVoiceAITuner(enabled: boolean, type: VoiceAiTunerType): number;
    protected getApiTypeFromEnableVoiceAITuner(enabled: boolean, type: VoiceAiTunerType): string;
    setLogFile(filePath: string): number;
    protected getApiTypeFromSetLogFile(filePath: string): string;
    setLogFilter(filter: LogFilterType): number;
    protected getApiTypeFromSetLogFilter(filter: LogFilterType): string;
    setLogLevel(level: LogLevel): number;
    protected getApiTypeFromSetLogLevel(level: LogLevel): string;
    setLogFileSize(fileSizeInKBytes: number): number;
    protected getApiTypeFromSetLogFileSize(fileSizeInKBytes: number): string;
    uploadLogFile(): string;
    protected getApiTypeFromUploadLogFile(): string;
    writeLog(level: LogLevel, fmt: string): number;
    protected getApiTypeFromWriteLog(level: LogLevel, fmt: string): string;
    setLocalRenderMode(renderMode: RenderModeType, mirrorMode?: VideoMirrorModeType): number;
    protected getApiTypeFromSetLocalRenderMode(renderMode: RenderModeType, mirrorMode?: VideoMirrorModeType): string;
    setRemoteRenderMode(uid: number, renderMode: RenderModeType, mirrorMode: VideoMirrorModeType): number;
    protected getApiTypeFromSetRemoteRenderMode(uid: number, renderMode: RenderModeType, mirrorMode: VideoMirrorModeType): string;
    setLocalRenderTargetFps(sourceType: VideoSourceType, targetFps: number): number;
    protected getApiTypeFromSetLocalRenderTargetFps(sourceType: VideoSourceType, targetFps: number): string;
    setRemoteRenderTargetFps(targetFps: number): number;
    protected getApiTypeFromSetRemoteRenderTargetFps(targetFps: number): string;
    setLocalVideoMirrorMode(mirrorMode: VideoMirrorModeType): number;
    protected getApiTypeFromSetLocalVideoMirrorMode(mirrorMode: VideoMirrorModeType): string;
    enableDualStreamMode(enabled: boolean, streamConfig?: SimulcastStreamConfig): number;
    protected getApiTypeFromEnableDualStreamMode(enabled: boolean, streamConfig?: SimulcastStreamConfig): string;
    setDualStreamMode(mode: SimulcastStreamMode, streamConfig?: SimulcastStreamConfig): number;
    protected getApiTypeFromSetDualStreamMode(mode: SimulcastStreamMode, streamConfig?: SimulcastStreamConfig): string;
    setSimulcastConfig(simulcastConfig: SimulcastConfig): number;
    protected getApiTypeFromSetSimulcastConfig(simulcastConfig: SimulcastConfig): string;
    enableCustomAudioLocalPlayback(trackId: number, enabled: boolean): number;
    protected getApiTypeFromEnableCustomAudioLocalPlayback(trackId: number, enabled: boolean): string;
    setRecordingAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): number;
    protected getApiTypeFromSetRecordingAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): string;
    setPlaybackAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): number;
    protected getApiTypeFromSetPlaybackAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): string;
    setMixedAudioFrameParameters(sampleRate: number, channel: number, samplesPerCall: number): number;
    protected getApiTypeFromSetMixedAudioFrameParameters(sampleRate: number, channel: number, samplesPerCall: number): string;
    setEarMonitoringAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): number;
    protected getApiTypeFromSetEarMonitoringAudioFrameParameters(sampleRate: number, channel: number, mode: RawAudioFrameOpModeType, samplesPerCall: number): string;
    setPlaybackAudioFrameBeforeMixingParameters(sampleRate: number, channel: number): number;
    protected getApiTypeFromSetPlaybackAudioFrameBeforeMixingParameters(sampleRate: number, channel: number): string;
    enableAudioSpectrumMonitor(intervalInMS?: number): number;
    protected getApiTypeFromEnableAudioSpectrumMonitor(intervalInMS?: number): string;
    disableAudioSpectrumMonitor(): number;
    protected getApiTypeFromDisableAudioSpectrumMonitor(): string;
    registerAudioSpectrumObserver(observer: IAudioSpectrumObserver): number;
    protected getApiTypeFromRegisterAudioSpectrumObserver(observer: IAudioSpectrumObserver): string;
    unregisterAudioSpectrumObserver(observer: IAudioSpectrumObserver): number;
    protected getApiTypeFromUnregisterAudioSpectrumObserver(observer: IAudioSpectrumObserver): string;
    adjustRecordingSignalVolume(volume: number): number;
    protected getApiTypeFromAdjustRecordingSignalVolume(volume: number): string;
    muteRecordingSignal(mute: boolean): number;
    protected getApiTypeFromMuteRecordingSignal(mute: boolean): string;
    adjustPlaybackSignalVolume(volume: number): number;
    protected getApiTypeFromAdjustPlaybackSignalVolume(volume: number): string;
    adjustUserPlaybackSignalVolume(uid: number, volume: number): number;
    protected getApiTypeFromAdjustUserPlaybackSignalVolume(uid: number, volume: number): string;
    setLocalPublishFallbackOption(option: StreamFallbackOptions): number;
    protected getApiTypeFromSetLocalPublishFallbackOption(option: StreamFallbackOptions): string;
    setRemoteSubscribeFallbackOption(option: StreamFallbackOptions): number;
    protected getApiTypeFromSetRemoteSubscribeFallbackOption(option: StreamFallbackOptions): string;
    setHighPriorityUserList(uidList: number[], uidNum: number, option: StreamFallbackOptions): number;
    protected getApiTypeFromSetHighPriorityUserList(uidList: number[], uidNum: number, option: StreamFallbackOptions): string;
    enableExtension(provider: string, extension: string, enable?: boolean, type?: MediaSourceType): number;
    protected getApiTypeFromEnableExtension(provider: string, extension: string, enable?: boolean, type?: MediaSourceType): string;
    setExtensionProperty(provider: string, extension: string, key: string, value: string, type?: MediaSourceType): number;
    protected getApiTypeFromSetExtensionProperty(provider: string, extension: string, key: string, value: string, type?: MediaSourceType): string;
    getExtensionProperty(provider: string, extension: string, key: string, bufLen: number, type?: MediaSourceType): string;
    protected getApiTypeFromGetExtensionProperty(provider: string, extension: string, key: string, bufLen: number, type?: MediaSourceType): string;
    enableLoopbackRecording(enabled: boolean, deviceName?: string): number;
    protected getApiTypeFromEnableLoopbackRecording(enabled: boolean, deviceName?: string): string;
    adjustLoopbackSignalVolume(volume: number): number;
    protected getApiTypeFromAdjustLoopbackSignalVolume(volume: number): string;
    getLoopbackRecordingVolume(): number;
    protected getApiTypeFromGetLoopbackRecordingVolume(): string;
    enableInEarMonitoring(enabled: boolean, includeAudioFilters: EarMonitoringFilterType): number;
    protected getApiTypeFromEnableInEarMonitoring(enabled: boolean, includeAudioFilters: EarMonitoringFilterType): string;
    setInEarMonitoringVolume(volume: number): number;
    protected getApiTypeFromSetInEarMonitoringVolume(volume: number): string;
    loadExtensionProvider(path: string, unloadAfterUse?: boolean): number;
    protected getApiTypeFromLoadExtensionProvider(path: string, unloadAfterUse?: boolean): string;
    setExtensionProviderProperty(provider: string, key: string, value: string): number;
    protected getApiTypeFromSetExtensionProviderProperty(provider: string, key: string, value: string): string;
    registerExtension(provider: string, extension: string, type?: MediaSourceType): number;
    protected getApiTypeFromRegisterExtension(provider: string, extension: string, type?: MediaSourceType): string;
    setCameraCapturerConfiguration(config: CameraCapturerConfiguration): number;
    protected getApiTypeFromSetCameraCapturerConfiguration(config: CameraCapturerConfiguration): string;
    createCustomVideoTrack(): number;
    protected getApiTypeFromCreateCustomVideoTrack(): string;
    createCustomEncodedVideoTrack(senderOption: SenderOptions): number;
    protected getApiTypeFromCreateCustomEncodedVideoTrack(senderOption: SenderOptions): string;
    destroyCustomVideoTrack(videoTrackId: number): number;
    protected getApiTypeFromDestroyCustomVideoTrack(videoTrackId: number): string;
    destroyCustomEncodedVideoTrack(videoTrackId: number): number;
    protected getApiTypeFromDestroyCustomEncodedVideoTrack(videoTrackId: number): string;
    switchCamera(): number;
    protected getApiTypeFromSwitchCamera(): string;
    isCameraZoomSupported(): boolean;
    protected getApiTypeFromIsCameraZoomSupported(): string;
    isCameraFaceDetectSupported(): boolean;
    protected getApiTypeFromIsCameraFaceDetectSupported(): string;
    isCameraTorchSupported(): boolean;
    protected getApiTypeFromIsCameraTorchSupported(): string;
    isCameraFocusSupported(): boolean;
    protected getApiTypeFromIsCameraFocusSupported(): string;
    isCameraAutoFocusFaceModeSupported(): boolean;
    protected getApiTypeFromIsCameraAutoFocusFaceModeSupported(): string;
    setCameraZoomFactor(factor: number): number;
    protected getApiTypeFromSetCameraZoomFactor(factor: number): string;
    enableFaceDetection(enabled: boolean): number;
    protected getApiTypeFromEnableFaceDetection(enabled: boolean): string;
    getCameraMaxZoomFactor(): number;
    protected getApiTypeFromGetCameraMaxZoomFactor(): string;
    setCameraFocusPositionInPreview(positionX: number, positionY: number): number;
    protected getApiTypeFromSetCameraFocusPositionInPreview(positionX: number, positionY: number): string;
    setCameraTorchOn(isOn: boolean): number;
    protected getApiTypeFromSetCameraTorchOn(isOn: boolean): string;
    setCameraAutoFocusFaceModeEnabled(enabled: boolean): number;
    protected getApiTypeFromSetCameraAutoFocusFaceModeEnabled(enabled: boolean): string;
    isCameraExposurePositionSupported(): boolean;
    protected getApiTypeFromIsCameraExposurePositionSupported(): string;
    setCameraExposurePosition(positionXinView: number, positionYinView: number): number;
    protected getApiTypeFromSetCameraExposurePosition(positionXinView: number, positionYinView: number): string;
    isCameraExposureSupported(): boolean;
    protected getApiTypeFromIsCameraExposureSupported(): string;
    setCameraExposureFactor(factor: number): number;
    protected getApiTypeFromSetCameraExposureFactor(factor: number): string;
    isCameraAutoExposureFaceModeSupported(): boolean;
    protected getApiTypeFromIsCameraAutoExposureFaceModeSupported(): string;
    setCameraAutoExposureFaceModeEnabled(enabled: boolean): number;
    protected getApiTypeFromSetCameraAutoExposureFaceModeEnabled(enabled: boolean): string;
    setCameraStabilizationMode(mode: CameraStabilizationMode): number;
    protected getApiTypeFromSetCameraStabilizationMode(mode: CameraStabilizationMode): string;
    setDefaultAudioRouteToSpeakerphone(defaultToSpeaker: boolean): number;
    protected getApiTypeFromSetDefaultAudioRouteToSpeakerphone(defaultToSpeaker: boolean): string;
    setEnableSpeakerphone(speakerOn: boolean): number;
    protected getApiTypeFromSetEnableSpeakerphone(speakerOn: boolean): string;
    isSpeakerphoneEnabled(): boolean;
    protected getApiTypeFromIsSpeakerphoneEnabled(): string;
    setRouteInCommunicationMode(route: number): number;
    protected getApiTypeFromSetRouteInCommunicationMode(route: number): string;
    isCameraCenterStageSupported(): boolean;
    protected getApiTypeFromIsCameraCenterStageSupported(): string;
    enableCameraCenterStage(enabled: boolean): number;
    protected getApiTypeFromEnableCameraCenterStage(enabled: boolean): string;
    getScreenCaptureSources(thumbSize: Size, iconSize: Size, includeScreen: boolean): ScreenCaptureSourceInfo[];
    protected getApiTypeFromGetScreenCaptureSources(thumbSize: Size, iconSize: Size, includeScreen: boolean): string;
    setAudioSessionOperationRestriction(restriction: AudioSessionOperationRestriction): number;
    protected getApiTypeFromSetAudioSessionOperationRestriction(restriction: AudioSessionOperationRestriction): string;
    startScreenCaptureByDisplayId(displayId: number, regionRect: Rectangle, captureParams: ScreenCaptureParameters): number;
    protected getApiTypeFromStartScreenCaptureByDisplayId(displayId: number, regionRect: Rectangle, captureParams: ScreenCaptureParameters): string;
    startScreenCaptureByScreenRect(screenRect: Rectangle, regionRect: Rectangle, captureParams: ScreenCaptureParameters): number;
    protected getApiTypeFromStartScreenCaptureByScreenRect(screenRect: Rectangle, regionRect: Rectangle, captureParams: ScreenCaptureParameters): string;
    getAudioDeviceInfo(): DeviceInfo;
    protected getApiTypeFromGetAudioDeviceInfo(): string;
    startScreenCaptureByWindowId(windowId: number, regionRect: Rectangle, captureParams: ScreenCaptureParameters): number;
    protected getApiTypeFromStartScreenCaptureByWindowId(windowId: number, regionRect: Rectangle, captureParams: ScreenCaptureParameters): string;
    setScreenCaptureContentHint(contentHint: VideoContentHint): number;
    protected getApiTypeFromSetScreenCaptureContentHint(contentHint: VideoContentHint): string;
    updateScreenCaptureRegion(regionRect: Rectangle): number;
    protected getApiTypeFromUpdateScreenCaptureRegion(regionRect: Rectangle): string;
    updateScreenCaptureParameters(captureParams: ScreenCaptureParameters): number;
    protected getApiTypeFromUpdateScreenCaptureParameters(captureParams: ScreenCaptureParameters): string;
    startScreenCapture(captureParams: ScreenCaptureParameters2): number;
    protected getApiTypeFromStartScreenCapture(captureParams: ScreenCaptureParameters2): string;
    updateScreenCapture(captureParams: ScreenCaptureParameters2): number;
    protected getApiTypeFromUpdateScreenCapture(captureParams: ScreenCaptureParameters2): string;
    queryScreenCaptureCapability(): number;
    protected getApiTypeFromQueryScreenCaptureCapability(): string;
    queryCameraFocalLengthCapability(): {
        focalLengthInfos: FocalLengthInfo[];
        size: number;
    };
    protected getApiTypeFromQueryCameraFocalLengthCapability(): string;
    setExternalMediaProjection(mediaProjection: any): number;
    protected getApiTypeFromSetExternalMediaProjection(mediaProjection: any): string;
    setScreenCaptureScenario(screenScenario: ScreenScenarioType): number;
    protected getApiTypeFromSetScreenCaptureScenario(screenScenario: ScreenScenarioType): string;
    stopScreenCapture(): number;
    protected getApiTypeFromStopScreenCapture(): string;
    getCallId(): string;
    protected getApiTypeFromGetCallId(): string;
    rate(callId: string, rating: number, description: string): number;
    protected getApiTypeFromRate(callId: string, rating: number, description: string): string;
    complain(callId: string, description: string): number;
    protected getApiTypeFromComplain(callId: string, description: string): string;
    startRtmpStreamWithoutTranscoding(url: string): number;
    protected getApiTypeFromStartRtmpStreamWithoutTranscoding(url: string): string;
    startRtmpStreamWithTranscoding(url: string, transcoding: LiveTranscoding): number;
    protected getApiTypeFromStartRtmpStreamWithTranscoding(url: string, transcoding: LiveTranscoding): string;
    updateRtmpTranscoding(transcoding: LiveTranscoding): number;
    protected getApiTypeFromUpdateRtmpTranscoding(transcoding: LiveTranscoding): string;
    startLocalVideoTranscoder(config: LocalTranscoderConfiguration): number;
    protected getApiTypeFromStartLocalVideoTranscoder(config: LocalTranscoderConfiguration): string;
    updateLocalTranscoderConfiguration(config: LocalTranscoderConfiguration): number;
    protected getApiTypeFromUpdateLocalTranscoderConfiguration(config: LocalTranscoderConfiguration): string;
    stopRtmpStream(url: string): number;
    protected getApiTypeFromStopRtmpStream(url: string): string;
    stopLocalVideoTranscoder(): number;
    protected getApiTypeFromStopLocalVideoTranscoder(): string;
    startLocalAudioMixer(config: LocalAudioMixerConfiguration): number;
    protected getApiTypeFromStartLocalAudioMixer(config: LocalAudioMixerConfiguration): string;
    updateLocalAudioMixerConfiguration(config: LocalAudioMixerConfiguration): number;
    protected getApiTypeFromUpdateLocalAudioMixerConfiguration(config: LocalAudioMixerConfiguration): string;
    stopLocalAudioMixer(): number;
    protected getApiTypeFromStopLocalAudioMixer(): string;
    startCameraCapture(sourceType: VideoSourceType, config: CameraCapturerConfiguration): number;
    protected getApiTypeFromStartCameraCapture(sourceType: VideoSourceType, config: CameraCapturerConfiguration): string;
    stopCameraCapture(sourceType: VideoSourceType): number;
    protected getApiTypeFromStopCameraCapture(sourceType: VideoSourceType): string;
    setCameraDeviceOrientation(type: VideoSourceType, orientation: VideoOrientation): number;
    protected getApiTypeFromSetCameraDeviceOrientation(type: VideoSourceType, orientation: VideoOrientation): string;
    setScreenCaptureOrientation(type: VideoSourceType, orientation: VideoOrientation): number;
    protected getApiTypeFromSetScreenCaptureOrientation(type: VideoSourceType, orientation: VideoOrientation): string;
    getConnectionState(): ConnectionStateType;
    protected getApiTypeFromGetConnectionState(): string;
    registerEventHandler(eventHandler: IRtcEngineEventHandler): boolean;
    protected getApiTypeFromRegisterEventHandler(eventHandler: IRtcEngineEventHandler): string;
    unregisterEventHandler(eventHandler: IRtcEngineEventHandler): boolean;
    protected getApiTypeFromUnregisterEventHandler(eventHandler: IRtcEngineEventHandler): string;
    setRemoteUserPriority(uid: number, userPriority: PriorityType): number;
    protected getApiTypeFromSetRemoteUserPriority(uid: number, userPriority: PriorityType): string;
    enableEncryption(enabled: boolean, config: EncryptionConfig): number;
    protected getApiTypeFromEnableEncryption(enabled: boolean, config: EncryptionConfig): string;
    createDataStream(config: DataStreamConfig): number;
    protected getApiTypeFromCreateDataStream(config: DataStreamConfig): string;
    sendStreamMessage(streamId: number, data: Uint8Array, length: number): number;
    protected getApiTypeFromSendStreamMessage(streamId: number, data: Uint8Array, length: number): string;
    addVideoWatermark(watermarkUrl: string, options: WatermarkOptions): number;
    protected getApiTypeFromAddVideoWatermark(watermarkUrl: string, options: WatermarkOptions): string;
    clearVideoWatermarks(): number;
    protected getApiTypeFromClearVideoWatermarks(): string;
    pauseAudio(): number;
    protected getApiTypeFromPauseAudio(): string;
    resumeAudio(): number;
    protected getApiTypeFromResumeAudio(): string;
    enableWebSdkInteroperability(enabled: boolean): number;
    protected getApiTypeFromEnableWebSdkInteroperability(enabled: boolean): string;
    sendCustomReportMessage(id: string, category: string, event: string, label: string, value: number): number;
    protected getApiTypeFromSendCustomReportMessage(id: string, category: string, event: string, label: string, value: number): string;
    registerMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): number;
    protected getApiTypeFromRegisterMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): string;
    unregisterMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): number;
    protected getApiTypeFromUnregisterMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): string;
    startAudioFrameDump(channelId: string, uid: number, location: string, uuid: string, passwd: string, durationMs: number, autoUpload: boolean): number;
    protected getApiTypeFromStartAudioFrameDump(channelId: string, uid: number, location: string, uuid: string, passwd: string, durationMs: number, autoUpload: boolean): string;
    stopAudioFrameDump(channelId: string, uid: number, location: string): number;
    protected getApiTypeFromStopAudioFrameDump(channelId: string, uid: number, location: string): string;
    setAINSMode(enabled: boolean, mode: AudioAinsMode): number;
    protected getApiTypeFromSetAINSMode(enabled: boolean, mode: AudioAinsMode): string;
    registerLocalUserAccount(appId: string, userAccount: string): number;
    protected getApiTypeFromRegisterLocalUserAccount(appId: string, userAccount: string): string;
    joinChannelWithUserAccount(token: string, channelId: string, userAccount: string, options?: ChannelMediaOptions): number;
    protected getApiTypeFromJoinChannelWithUserAccount(token: string, channelId: string, userAccount: string, options?: ChannelMediaOptions): string;
    joinChannelWithUserAccountEx(token: string, channelId: string, userAccount: string, options: ChannelMediaOptions): number;
    protected getApiTypeFromJoinChannelWithUserAccountEx(token: string, channelId: string, userAccount: string, options: ChannelMediaOptions): string;
    getUserInfoByUserAccount(userAccount: string): UserInfo;
    protected getApiTypeFromGetUserInfoByUserAccount(userAccount: string): string;
    getUserInfoByUid(uid: number): UserInfo;
    protected getApiTypeFromGetUserInfoByUid(uid: number): string;
    startOrUpdateChannelMediaRelay(configuration: ChannelMediaRelayConfiguration): number;
    protected getApiTypeFromStartOrUpdateChannelMediaRelay(configuration: ChannelMediaRelayConfiguration): string;
    stopChannelMediaRelay(): number;
    protected getApiTypeFromStopChannelMediaRelay(): string;
    pauseAllChannelMediaRelay(): number;
    protected getApiTypeFromPauseAllChannelMediaRelay(): string;
    resumeAllChannelMediaRelay(): number;
    protected getApiTypeFromResumeAllChannelMediaRelay(): string;
    setDirectCdnStreamingAudioConfiguration(profile: AudioProfileType): number;
    protected getApiTypeFromSetDirectCdnStreamingAudioConfiguration(profile: AudioProfileType): string;
    setDirectCdnStreamingVideoConfiguration(config: VideoEncoderConfiguration): number;
    protected getApiTypeFromSetDirectCdnStreamingVideoConfiguration(config: VideoEncoderConfiguration): string;
    startDirectCdnStreaming(eventHandler: IDirectCdnStreamingEventHandler, publishUrl: string, options: DirectCdnStreamingMediaOptions): number;
    protected getApiTypeFromStartDirectCdnStreaming(eventHandler: IDirectCdnStreamingEventHandler, publishUrl: string, options: DirectCdnStreamingMediaOptions): string;
    stopDirectCdnStreaming(): number;
    protected getApiTypeFromStopDirectCdnStreaming(): string;
    updateDirectCdnStreamingMediaOptions(options: DirectCdnStreamingMediaOptions): number;
    protected getApiTypeFromUpdateDirectCdnStreamingMediaOptions(options: DirectCdnStreamingMediaOptions): string;
    startRhythmPlayer(sound1: string, sound2: string, config: AgoraRhythmPlayerConfig): number;
    protected getApiTypeFromStartRhythmPlayer(sound1: string, sound2: string, config: AgoraRhythmPlayerConfig): string;
    stopRhythmPlayer(): number;
    protected getApiTypeFromStopRhythmPlayer(): string;
    configRhythmPlayer(config: AgoraRhythmPlayerConfig): number;
    protected getApiTypeFromConfigRhythmPlayer(config: AgoraRhythmPlayerConfig): string;
    takeSnapshot(uid: number, filePath: string): number;
    protected getApiTypeFromTakeSnapshot(uid: number, filePath: string): string;
    enableContentInspect(enabled: boolean, config: ContentInspectConfig): number;
    protected getApiTypeFromEnableContentInspect(enabled: boolean, config: ContentInspectConfig): string;
    adjustCustomAudioPublishVolume(trackId: number, volume: number): number;
    protected getApiTypeFromAdjustCustomAudioPublishVolume(trackId: number, volume: number): string;
    adjustCustomAudioPlayoutVolume(trackId: number, volume: number): number;
    protected getApiTypeFromAdjustCustomAudioPlayoutVolume(trackId: number, volume: number): string;
    setCloudProxy(proxyType: CloudProxyType): number;
    protected getApiTypeFromSetCloudProxy(proxyType: CloudProxyType): string;
    setLocalAccessPoint(config: LocalAccessPointConfiguration): number;
    protected getApiTypeFromSetLocalAccessPoint(config: LocalAccessPointConfiguration): string;
    setAdvancedAudioOptions(options: AdvancedAudioOptions, sourceType?: number): number;
    protected getApiTypeFromSetAdvancedAudioOptions(options: AdvancedAudioOptions, sourceType?: number): string;
    setAVSyncSource(channelId: string, uid: number): number;
    protected getApiTypeFromSetAVSyncSource(channelId: string, uid: number): string;
    enableVideoImageSource(enable: boolean, options: ImageTrackOptions): number;
    protected getApiTypeFromEnableVideoImageSource(enable: boolean, options: ImageTrackOptions): string;
    getCurrentMonotonicTimeInMs(): number;
    protected getApiTypeFromGetCurrentMonotonicTimeInMs(): string;
    enableWirelessAccelerate(enabled: boolean): number;
    protected getApiTypeFromEnableWirelessAccelerate(enabled: boolean): string;
    getNetworkType(): number;
    protected getApiTypeFromGetNetworkType(): string;
    setParameters(parameters: string): number;
    protected getApiTypeFromSetParameters(parameters: string): string;
    startMediaRenderingTracing(): number;
    protected getApiTypeFromStartMediaRenderingTracing(): string;
    enableInstantMediaRendering(): number;
    protected getApiTypeFromEnableInstantMediaRendering(): string;
    getNtpWallTimeInMs(): number;
    protected getApiTypeFromGetNtpWallTimeInMs(): string;
    isFeatureAvailableOnDevice(type: FeatureType): boolean;
    protected getApiTypeFromIsFeatureAvailableOnDevice(type: FeatureType): string;
    sendAudioMetadata(metadata: string, length: number): number;
    protected getApiTypeFromSendAudioMetadata(metadata: string, length: number): string;
    queryHDRCapability(videoModule: VideoModuleType): HdrCapability;
    protected getApiTypeFromQueryHDRCapability(videoModule: VideoModuleType): string;
    startScreenCaptureBySourceType(sourceType: VideoSourceType, config: ScreenCaptureConfiguration): number;
    protected getApiTypeFromStartScreenCaptureBySourceType(sourceType: VideoSourceType, config: ScreenCaptureConfiguration): string;
    stopScreenCaptureBySourceType(sourceType: VideoSourceType): number;
    protected getApiTypeFromStopScreenCaptureBySourceType(sourceType: VideoSourceType): string;
    release(sync?: boolean): void;
    protected getApiTypeFromRelease(sync?: boolean): string;
    startPreviewWithoutSourceType(): number;
    protected getApiTypeFromStartPreviewWithoutSourceType(): string;
    getAudioDeviceManager(): IAudioDeviceManager;
    protected getApiTypeFromGetAudioDeviceManager(): string;
    getVideoDeviceManager(): IVideoDeviceManager;
    protected getApiTypeFromGetVideoDeviceManager(): string;
    getMusicContentCenter(): IMusicContentCenter;
    protected getApiTypeFromGetMusicContentCenter(): string;
    getMediaEngine(): IMediaEngine;
    protected getApiTypeFromGetMediaEngine(): string;
    getLocalSpatialAudioEngine(): ILocalSpatialAudioEngine;
    protected getApiTypeFromGetLocalSpatialAudioEngine(): string;
    getH265Transcoder(): IH265Transcoder;
    protected getApiTypeFromGetH265Transcoder(): string;
    sendMetaData(metadata: Metadata, sourceType: VideoSourceType): number;
    protected getApiTypeFromSendMetaData(metadata: Metadata, sourceType: VideoSourceType): string;
    setMaxMetadataSize(size: number): number;
    protected getApiTypeFromSetMaxMetadataSize(size: number): string;
    destroyRendererByView(view: any): void;
    protected getApiTypeFromDestroyRendererByView(view: any): string;
    destroyRendererByConfig(sourceType: VideoSourceType, channelId?: string, uid?: number): void;
    protected getApiTypeFromDestroyRendererByConfig(sourceType: VideoSourceType, channelId?: string, uid?: number): string;
    unregisterAudioEncodedFrameObserver(observer: IAudioEncodedFrameObserver): number;
    protected getApiTypeFromUnregisterAudioEncodedFrameObserver(observer: IAudioEncodedFrameObserver): string;
    getNativeHandle(): number;
    protected getApiTypeFromGetNativeHandle(): string;
    takeSnapshotWithConfig(uid: number, config: SnapshotConfig): number;
    protected getApiTypeFromTakeSnapshotWithConfig(uid: number, config: SnapshotConfig): string;
}
//# sourceMappingURL=IAgoraRtcEngineImpl.d.ts.map