# 🎬 Agora SDK Setup Guide

## 📋 Prerequisites

1. **Agora Account**: Sign up at [https://console.agora.io/](https://console.agora.io/)
2. **App ID**: Create a new project and get your App ID
3. **Development Build**: EAS development build with Agora SDK

## 🔧 Setup Steps

### Step 1: Get Agora App ID

1. Go to [Agora Console](https://console.agora.io/)
2. Sign up or log in
3. Create a new project
4. Copy your **App ID**

### Step 2: Configure Environment

1. Open `.env` file in the project root
2. Replace the App ID:
   ```
   EXPO_PUBLIC_AGORA_APP_ID=your_actual_app_id_here
   ```

### Step 3: Test Configuration

1. Start the development server:
   ```bash
   npx expo start --dev-client
   ```

2. Open the app on your device/emulator
3. Navigate to "Go Live" tab
4. Check if <PERSON>gor<PERSON> initializes without errors

## 🎯 Features Enabled

With proper Agora configuration, you'll have:

- ✅ **Real-time video streaming**
- ✅ **Audio communication**
- ✅ **Camera controls** (switch, mute)
- ✅ **Live broadcasting**
- ✅ **Multi-user support**

## 🔍 Troubleshooting

### Common Issues:

**1. "Agora SDK not available" message:**
- Ensure you're using the EAS development build
- Check that react-native-agora is properly installed

**2. "Invalid App ID" error:**
- Verify your App ID in `.env` file
- Make sure there are no extra spaces or quotes

**3. Camera permission denied:**
- Grant camera/microphone permissions in device settings
- Restart the app after granting permissions

**4. Build errors:**
- Run cache cleanup: `npm cache clean --force`
- Reinstall dependencies: `rm -rf node_modules && npm install`
- Rebuild with EAS: `npx eas build --profile development --platform android`

## 📱 Testing Checklist

- [ ] App loads without crashes
- [ ] "Go Live" tab accessible
- [ ] Camera preview shows (not mock)
- [ ] Permission requests appear
- [ ] Stream title/description can be entered
- [ ] "Start Live Stream" button works
- [ ] Audio/video controls functional

## 🚀 Next Steps

1. **Get Agora App ID** from console
2. **Update .env file** with your App ID
3. **Test live streaming** functionality
4. **Implement token authentication** (for production)
5. **Add advanced features** (screen sharing, effects, etc.)

## 📞 Support

- **Agora Documentation**: [https://docs.agora.io/](https://docs.agora.io/)
- **React Native Guide**: [https://docs.agora.io/en/video-calling/get-started/get-started-sdk?platform=react-native](https://docs.agora.io/en/video-calling/get-started/get-started-sdk?platform=react-native)
- **Community Forum**: [https://community.agora.io/](https://community.agora.io/)
