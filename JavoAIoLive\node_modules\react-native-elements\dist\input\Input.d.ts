import React from 'react';
import { TextInput, Animated, ViewStyle, StyleProp, TextStyle, TextInputProps } from 'react-native';
import { IconNode } from '../icons/Icon';
import { ThemeProps } from '../config';
export declare type InputProps = React.ComponentPropsWithRef<typeof TextInput> & {
    containerStyle?: StyleProp<ViewStyle>;
    disabled?: boolean;
    disabledInputStyle?: StyleProp<TextStyle>;
    inputContainerStyle?: StyleProp<ViewStyle>;
    leftIcon?: IconNode;
    leftIconContainerStyle?: StyleProp<ViewStyle>;
    rightIcon?: IconNode;
    rightIconContainerStyle?: StyleProp<ViewStyle>;
    inputStyle?: StyleProp<TextStyle>;
    InputComponent?: typeof React.Component;
    errorProps?: object;
    errorStyle?: StyleProp<TextStyle>;
    errorMessage?: string;
    label?: string | React.ReactNode;
    labelStyle?: StyleProp<TextStyle>;
    labelProps?: object;
    renderErrorMessage?: boolean;
};
declare class Input extends React.Component<InputProps & Partial<ThemeProps<InputProps>>> {
    input: any;
    shakeAnimationValue: Animated.Value;
    focus(): void;
    blur(): void;
    clear(): void;
    isFocused(): boolean;
    setNativeProps(nativeProps: Partial<TextInputProps>): void;
    shake: () => void;
    render(): JSX.Element;
}
export { Input };
declare const _default: React.FunctionComponent<Omit<TextInputProps & React.RefAttributes<TextInput> & {
    containerStyle?: StyleProp<ViewStyle>;
    disabled?: boolean;
    disabledInputStyle?: StyleProp<TextStyle>;
    inputContainerStyle?: StyleProp<ViewStyle>;
    leftIcon?: IconNode;
    leftIconContainerStyle?: StyleProp<ViewStyle>;
    rightIcon?: IconNode;
    rightIconContainerStyle?: StyleProp<ViewStyle>;
    inputStyle?: StyleProp<TextStyle>;
    InputComponent?: typeof React.Component;
    errorProps?: object;
    errorStyle?: StyleProp<TextStyle>;
    errorMessage?: string;
    label?: React.ReactNode;
    labelStyle?: StyleProp<TextStyle>;
    labelProps?: object;
    renderErrorMessage?: boolean;
} & Partial<ThemeProps<InputProps>>, keyof ThemeProps<T>>> | React.ForwardRefExoticComponent<TextInputProps & React.RefAttributes<TextInput> & {
    containerStyle?: StyleProp<ViewStyle>;
    disabled?: boolean;
    disabledInputStyle?: StyleProp<TextStyle>;
    inputContainerStyle?: StyleProp<ViewStyle>;
    leftIcon?: IconNode;
    leftIconContainerStyle?: StyleProp<ViewStyle>;
    rightIcon?: IconNode;
    rightIconContainerStyle?: StyleProp<ViewStyle>;
    inputStyle?: StyleProp<TextStyle>;
    InputComponent?: typeof React.Component;
    errorProps?: object;
    errorStyle?: StyleProp<TextStyle>;
    errorMessage?: string;
    label?: React.ReactNode;
    labelStyle?: StyleProp<TextStyle>;
    labelProps?: object;
    renderErrorMessage?: boolean;
} & Partial<ThemeProps<InputProps>>>;
export default _default;
