#!/bin/bash
# Copyright (c) Meta Platforms, Inc. and affiliates.
# Copyright 2018-present 650 Industries. All rights reserved.
#
# @generated by expo-module-scripts
#
# USAGE:
# ./with-node.sh command

CURR_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"

# Start with a default
NODE_BINARY=$(command -v node)
export NODE_BINARY

# Override the default with the global environment
ENV_PATH="$PODS_ROOT/../.xcode.env"
if [[ -f "$ENV_PATH" ]]; then
  source "$ENV_PATH"
fi

# Override the global with the local environment
LOCAL_ENV_PATH="${ENV_PATH}.local"
if [[ -f "$LOCAL_ENV_PATH" ]]; then
  source "$LOCAL_ENV_PATH"
fi

if [[ -n "$NODE_BINARY" && -x "$NODE_BINARY" ]]; then
  echo "Node found at: ${NODE_BINARY}"
else
  cat >&2 << EOF
[ERROR] Could not find "node" while running an Xcode build script. You need to specify the path to your Node.js executable by defining an environment variable named NODE_BINARY in your project's .xcode.env or .xcode.env.local file. You can set this up quickly by running:

  echo "export NODE_BINARY=\$(command -v node)" >> .xcode.env

in the ios folder of your project.
EOF
  exit 1
fi

# Execute argument, if present
if [[ "$#" -gt 0 ]]; then
  "$NODE_BINARY" "$@"
fi
