{"version": 3, "file": "AV.types.js", "sourceRoot": "", "sources": ["../src/AV.types.ts"], "names": [], "mappings": "AAEA,OAAO,UAAU,MAAM,cAAc,CAAC;AAEtC,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,sBAaX;AAbD,WAAY,sBAAsB;IAChC;;OAEG;IACH,uDAAM,UAAU,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,SAAA,CAAA;IACpE;;OAEG;IACH,0DAAS,UAAU,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,YAAA,CAAA;IAC1E;;OAEG;IACH,wDAAO,UAAU,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,UAAA,CAAA;AACxE,CAAC,EAbW,sBAAsB,KAAtB,sBAAsB,QAajC", "sourcesContent": ["import { Asset } from 'expo-asset';\n\nimport ExponentAV from './ExponentAV';\n\n// @needsAudit\n/**\n * Check [official Apple documentation](https://developer.apple.com/documentation/avfoundation/audio_settings/time_pitch_algorithm_settings) for more information.\n */\nexport enum PitchCorrectionQuality {\n  /**\n   * Equivalent to `AVAudioTimePitchAlgorithmLowQualityZeroLatency`.\n   */\n  Low = ExponentAV && ExponentAV.Qualities && ExponentAV.Qualities.Low,\n  /**\n   * Equivalent to `AVAudioTimePitchAlgorithmTimeDomain`.\n   */\n  Medium = ExponentAV && ExponentAV.Qualities && ExponentAV.Qualities.Medium,\n  /**\n   * Equivalent to `AVAudioTimePitchAlgorithmSpectral`.\n   */\n  High = ExponentAV && ExponentAV.Qualities && ExponentAV.Qualities.High,\n}\n\n// @needsAudit\n/**\n * The following forms of source are supported:\n * - A dictionary of the form `AVPlaybackSourceObject`.\n *   The `overrideFileExtensionAndroid` property may come in handy if the player receives an URL like `example.com/play` which redirects to `example.com/player.m3u8`.\n *   Setting this property to `m3u8` would allow the Android player to properly infer the content type of the media and use proper media file reader.\n * - `require('path/to/file')` for a media file asset in the source code directory.\n * - An [`Asset`](./asset) object for a media file asset.\n *\n * The [iOS developer documentation](https://developer.apple.com/library/archive/documentation/MacOSX/Conceptual/OSX_Technology_Overview/MediaLayer/MediaLayer.html)\n * lists the audio and video formats supported on iOS.\n *\n * There are two sets of audio and video formats supported on Android: [formats supported by ExoPlayer](https://exoplayer.dev/supported-formats.html)\n * and [formats supported by Android's MediaPlayer](https://developer.android.com/guide/topics/media/platform/supported-formats#formats-table).\n * Expo uses ExoPlayer implementation by default. To use `MediaPlayer`, add `androidImplementation: 'MediaPlayer'` to the initial status of the AV object.\n */\nexport type AVPlaybackSource = number | AVPlaybackSourceObject | Asset;\n\n// @needsAudit\n/**\n * One of the possible forms of the `AVPlaybackSource`.\n */\nexport type AVPlaybackSourceObject = {\n  /**\n   * A network URL pointing to a media file.\n   */\n  uri: string;\n  /**\n   * An optional string overriding extension inferred from the URL.\n   * @platform android\n   */\n  overrideFileExtensionAndroid?: string;\n  /**\n   * An optional headers object passed in a network request.\n   */\n  headers?: Record<string, string>;\n};\n\n/**\n * @hidden\n */\nexport type AVPlaybackNativeSource = {\n  uri: string;\n  overridingExtension?: string | null;\n  headers?: Record<string, string>;\n};\n\n// @needsAudit\n/**\n * Object passed to the `onMetadataUpdate` function.\n */\nexport type AVMetadata = {\n  /**\n   * A string with the title of the sound object.\n   */\n  title?: string;\n};\n\n// @needsAudit\n/**\n * This is the structure returned from all playback API calls and describes the state of the `playbackObject` at that point in time.\n * It can take a form of `AVPlaybackStatusSuccess` or `AVPlaybackStatusError` based on the `playbackObject` load status.\n */\nexport type AVPlaybackStatus = AVPlaybackStatusError | AVPlaybackStatusSuccess;\n\n// @needsAudit\nexport type AVPlaybackStatusError = {\n  /**\n   * A boolean set to `false`.\n   */\n  isLoaded: false;\n  /**\n   * Underlying implementation to use (when set to `MediaPlayer` it uses [Android's MediaPlayer](https://developer.android.com/reference/android/media/MediaPlayer.html),\n   * uses [ExoPlayer](https://exoplayer.dev/) otherwise). You may need to use this property if you're trying to play an item unsupported by ExoPlayer\n   * ([formats supported by ExoPlayer](https://exoplayer.dev/supported-formats.html), [formats supported by Android's MediaPlayer](https://developer.android.com/guide/topics/media/platform/supported-formats#formats-table)).\n   *\n   * Note that setting this property takes effect only when the AV object is just being created (toggling its value later will do nothing).\n   *\n   * @platform android\n   */\n  androidImplementation?: string;\n  /**\n   * A string only present if the `playbackObject` just encountered a fatal error and forced unload.\n   * Populated exactly once when an error forces the object to unload.\n   */\n  error?: string;\n};\n\n// @needsAudit\nexport type AVPlaybackStatusSuccess = {\n  /**\n   * A boolean set to `true`.\n   */\n  isLoaded: true;\n  /**\n   * Underlying implementation to use (when set to `MediaPlayer` it uses [Android's MediaPlayer](https://developer.android.com/reference/android/media/MediaPlayer.html),\n   * uses [ExoPlayer](https://exoplayer.dev/) otherwise). You may need to use this property if you're trying to play an item unsupported by ExoPlayer\n   * ([formats supported by ExoPlayer](https://exoplayer.dev/supported-formats.html), [formats supported by Android's MediaPlayer](https://developer.android.com/guide/topics/media/platform/supported-formats#formats-table)).\n   *\n   * Note that setting this property takes effect only when the AV object is just being created (toggling its value later will do nothing).\n   *\n   * @platform android\n   */\n  androidImplementation?: string;\n  /**\n   * The location of the media source.\n   */\n  uri: string;\n  /**\n   * The minimum interval in milliseconds between calls of `onPlaybackStatusUpdate`. See `setOnPlaybackStatusUpdate()` for details.\n   */\n  progressUpdateIntervalMillis: number;\n  /**\n   * The duration of the media in milliseconds. This is only present if the media has a duration.\n   * > Note that in some cases, a media file's duration is readable on Android, but not on iOS.\n   */\n  durationMillis?: number;\n  /**\n   * The current position of playback in milliseconds.\n   */\n  positionMillis: number;\n  /**\n   * The position until which the media has been buffered into memory. Like `durationMillis`, this is only present in some cases.\n   */\n  playableDurationMillis?: number;\n  // @docsMissing\n  seekMillisToleranceBefore?: number;\n  // @docsMissing\n  seekMillisToleranceAfter?: number;\n  /**\n   * A boolean describing if the media is supposed to play.\n   */\n  shouldPlay: boolean;\n  /**\n   * A boolean describing if the media is currently playing.\n   */\n  isPlaying: boolean;\n  /**\n   * A boolean describing if the media is currently buffering.\n   */\n  isBuffering: boolean;\n  /**\n   * The current rate of the media.\n   */\n  rate: number;\n  /**\n   * A boolean describing if we are correcting the pitch for a changed rate.\n   */\n  shouldCorrectPitch: boolean;\n  /**\n   * iOS time pitch algorithm setting. See `setRateAsync` for details.\n   */\n  pitchCorrectionQuality?: PitchCorrectionQuality;\n  /**\n   * The current volume of the audio for this media.\n   */\n  volume: number;\n  /**\n   * A boolean describing if the audio of this media is currently muted.\n   */\n  isMuted: boolean;\n  /**\n   * The current audio panning value of the audio for this media.\n   */\n  audioPan: number;\n  /**\n   * A boolean describing if the media is currently looping.\n   */\n  isLooping: boolean;\n  /**\n   * A boolean describing if the media just played to completion at the time that this status was received.\n   * When the media plays to completion, the function passed in `setOnPlaybackStatusUpdate()` is called exactly once\n   * with `didJustFinish` set to `true`. `didJustFinish` is never `true` in any other case.\n   */\n  didJustFinish: boolean;\n};\n\n// @needsAudit\n/**\n * This is the structure passed to `setStatusAsync()` to modify the state of the `playbackObject`.\n */\nexport type AVPlaybackStatusToSet = {\n  /**\n   * Underlying implementation to use (when set to `MediaPlayer` it uses [Android's MediaPlayer](https://developer.android.com/reference/android/media/MediaPlayer.html),\n   * uses [ExoPlayer](https://exoplayer.dev/) otherwise). You may need to use this property if you're trying to play an item unsupported by ExoPlayer\n   * ([formats supported by ExoPlayer](https://exoplayer.dev/supported-formats.html), [formats supported by Android's MediaPlayer](https://developer.android.com/guide/topics/media/platform/supported-formats#formats-table)).\n   *\n   * Note that setting this property takes effect only when the AV object is just being created (toggling its value later will do nothing).\n   *\n   * @platform android\n   */\n  androidImplementation?: string;\n  /**\n   * The minimum interval in milliseconds between calls of `onPlaybackStatusUpdate`. See `setOnPlaybackStatusUpdate()` for details.\n   */\n  progressUpdateIntervalMillis?: number;\n  /**\n   * The current position of playback in milliseconds.\n   */\n  positionMillis?: number;\n  // @docsMissing\n  seekMillisToleranceBefore?: number;\n  // @docsMissing\n  seekMillisToleranceAfter?: number;\n  /**\n   * A boolean describing if the media is supposed to play.\n   */\n  shouldPlay?: boolean;\n  /**\n   * The current rate of the media.\n   * @platform android API 23+\n   * @platform ios\n   */\n  rate?: number;\n  /**\n   * A boolean describing if we are correcting the pitch for a changed rate.\n   */\n  shouldCorrectPitch?: boolean;\n  /**\n   * The current volume of the audio for this media.\n   * > Note that this only affect the audio of this `playbackObject` and do NOT affect the system volume.\n   */\n  volume?: number;\n  /**\n   * A boolean describing if the audio of this media is currently muted.\n   * > Note that this only affect the audio of this `playbackObject` and do NOT affect the system volume.\n   */\n  isMuted?: boolean;\n  /**\n   * The current audio panning value of the audio for this media.\n   * > Note that this only affect the audio of this `playbackObject` and do NOT affect the system volume.\n   * > Also note that this is only available when the video was loaded using `androidImplementation: 'MediaPlayer'`\n   * @platform android\n   */\n  audioPan?: number;\n  /**\n   * A boolean describing if the media is currently looping.\n   */\n  isLooping?: boolean;\n  /**\n   * iOS time pitch algorithm setting. See `setRateAsync` for details.\n   */\n  pitchCorrectionQuality?: PitchCorrectionQuality;\n};\n\n// @docsMissing\nexport type AVPlaybackTolerance = { toleranceMillisBefore?: number; toleranceMillisAfter?: number };\n"]}