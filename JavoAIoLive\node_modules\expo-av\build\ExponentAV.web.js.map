{"version": 3, "file": "ExponentAV.web.js", "sourceRoot": "", "sources": ["../src/ExponentAV.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACzE,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAIlD,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AAErE,KAAK,UAAU,2BAA2B,CACxC,IAAwC;IAExC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK;QAAE,OAAO,IAAI,CAAC;IAEtF,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9D,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,SAAS;gBACZ,OAAO,gBAAgB,CAAC,OAAO,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,gBAAgB,CAAC,MAAM,CAAC;YACjC;gBACE,OAAO,gBAAgB,CAAC,YAAY,CAAC;QACzC,CAAC;IACH,CAAC;IAAC,MAAM,CAAC;QACP,0IAA0I;QAC1I,OAAO,gBAAgB,CAAC,YAAY,CAAC;IACvC,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,WAAmC;IACvD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QAClE,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IAED,iFAAiF;IACjF,+DAA+D;IAC/D,oEAAoE;IAEpE,yDAAyD;IACzD,MAAM,YAAY;IAChB,yHAAyH;IACzH,SAAS,CAAC,YAAY;QACtB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,eAAe;QACzB;YACE,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACzD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;YACf,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;IAEJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,6DAA6D;QAC7D,wCAAwC;QACxC,6EAA6E;QAC7E,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAwB;IAClD,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,KAAK,EAAE,SAAS;SACjB,CAAC;IACJ,CAAC;IAED,MAAM,SAAS,GAAG,CAAC,CAAC,CAClB,KAAK,CAAC,WAAW,GAAG,CAAC;QACrB,CAAC,KAAK,CAAC,MAAM;QACb,CAAC,KAAK,CAAC,KAAK;QACZ,KAAK,CAAC,UAAU,GAAG,CAAC,CACrB,CAAC;IAEF,MAAM,MAAM,GAAqB;QAC/B,QAAQ,EAAE,IAAI;QACd,GAAG,EAAE,KAAK,CAAC,GAAG;QACd,4BAA4B,EAAE,GAAG,EAAE,yCAAyC;QAC5E,cAAc,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI;QACrC,cAAc,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI;QACxC,iDAAiD;QACjD,qCAAqC;QACrC,oCAAoC;QACpC,UAAU,EAAE,KAAK,CAAC,QAAQ;QAC1B,SAAS;QACT,WAAW,EAAE,KAAK,EAAE,gBAAgB;QACpC,IAAI,EAAE,KAAK,CAAC,YAAY;QACxB,8HAA8H;QAC9H,kBAAkB,EAAE,KAAK;QACzB,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,KAAK,CAAC,KAAK;QACpB,SAAS,EAAE,KAAK,CAAC,IAAI;QACrB,aAAa,EAAE,KAAK,CAAC,KAAK;KAC3B,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,iBAAiB,CAC9B,KAAuB,EACvB,MAA6B;IAE7B,IAAI,MAAM,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;QACxC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;IACnD,CAAC;IACD,2DAA2D;IAC3D,8EAA8E;IAC9E,IAAI;IACJ,wDAAwD;IACxD,wEAAwE;IACxE,IAAI;IACJ,uDAAuD;IACvD,sEAAsE;IACtE,IAAI;IACJ,iDAAiD;IACjD,0DAA0D;IAC1D,IAAI;IACJ,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC9B,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC;IACnC,CAAC;IACD,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QAC5C,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,kBAAkB,CAAC;IACnD,CAAC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAChC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC/B,CAAC;IACD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QACjC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC;IAC/B,CAAC;IACD,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QACnC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;IAChC,CAAC;IAED,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACnC,CAAC;AAED,IAAI,aAAa,GAAyB,IAAI,CAAC;AAC/C,IAAI,oCAAoC,GAAW,CAAC,CAAC;AACrD,IAAI,oCAAoC,GAAW,CAAC,CAAC;AACrD,IAAI,wBAAwB,GAAY,KAAK,CAAC;AAE9C,SAAS,8BAA8B;IACrC,IAAI,QAAQ,GAAG,oCAAoC,CAAC;IACpD,IAAI,wBAAwB,IAAI,oCAAoC,GAAG,CAAC,EAAE,CAAC;QACzE,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,oCAAoC,CAAC;IAChE,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,eAAe;IACb,KAAK,CAAC,iBAAiB,CAAC,OAAyB;QAC/C,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,YAAY,CAChB,OAAyB,EACzB,YAAoC,EACpC,iBAAwC;QAExC,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,cAAc,CAAC,OAAyB;QAC5C,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,iBAAiB,CACrB,OAAyB,EACzB,MAA6B;QAE7B,OAAO,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IACD,KAAK,CAAC,WAAW,CACf,OAAyB,EACzB,MAA6B;QAE7B,OAAO,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IACD,WAAW;IACX,KAAK,CAAC,YAAY,KAAI,CAAC;IACvB,KAAK,CAAC,iBAAiB,KAAI,CAAC;IAC5B,KAAK,CAAC,iBAAiB,CAAC,OAAyB;QAC/C,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,YAAY,CAChB,YAA0D,EAC1D,iBAAwC;QAExC,MAAM,MAAM,GAAG,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC;QAClF,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QAEhC,KAAK,CAAC,YAAY,GAAG,GAAG,EAAE;YACxB,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACjD,GAAG,EAAE,KAAK;gBACV,MAAM,EAAE,kBAAkB,CAAC,KAAK,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,OAAO,GAAG,GAAG,EAAE;YACnB,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5C,GAAG,EAAE,KAAK;gBACV,KAAK,EAAE,KAAK,CAAC,KAAM,CAAC,OAAO;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;QAEjE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzB,CAAC;IACD,KAAK,CAAC,cAAc,CAAC,OAAyB;QAC5C,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,iBAAiB,CACrB,OAAyB,EACzB,MAA6B;QAE7B,OAAO,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IACD,KAAK,CAAC,WAAW,CACf,OAAyB,EACzB,MAA6B;QAE7B,OAAO,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,eAAe;IACf,uDAAuD;IACvD,KAAK,CAAC,uBAAuB;QAC3B,OAAO;YACL,SAAS,EAAE,aAAa,EAAE,KAAK,KAAK,WAAW,IAAI,aAAa,EAAE,KAAK,KAAK,UAAU;YACtF,WAAW,EAAE,aAAa,EAAE,KAAK,KAAK,WAAW;YACjD,eAAe,EAAE,KAAK;YACtB,cAAc,EAAE,8BAA8B,EAAE;YAChD,GAAG,EAAE,IAAI;SACV,CAAC;IACJ,CAAC;IACD,mCAAmC;IACnC,KAAK,CAAC,oBAAoB,CAAC,OAAY;QAKrC,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,oCAAoC,GAAG,CAAC,CAAC;QACzC,oCAAoC,GAAG,CAAC,CAAC;QAEzC,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAEnD,aAAa,GAAG,IAAI,MAAM,CAAC,aAAa,CACtC,MAAM,EACN,OAAO,EAAE,GAAG,IAAI,uBAAuB,CAAC,YAAY,CAAC,GAAG,CACzD,CAAC;QAEF,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3C,oCAAoC,GAAG,8BAA8B,EAAE,CAAC;YACxE,wBAAwB,GAAG,KAAK,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;YAC5C,oCAAoC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAClD,wBAAwB,GAAG,IAAI,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC3C,oCAAoC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAClD,oCAAoC,GAAG,CAAC,CAAC;YACzC,wBAAwB,GAAG,IAAI,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;YAC1C,oCAAoC,GAAG,8BAA8B,EAAE,CAAC;YACxE,wBAAwB,GAAG,KAAK,CAAC;YAEjC,sCAAsC;YACtC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEhE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CACb,iJAAiJ,CAClJ,CAAC;QACJ,CAAC;QAED,IAAI,aAAa,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACrC,aAAa,CAAC,MAAM,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACxC,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CACb,iJAAiJ,CAClJ,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,aAAa,CAAC,KAAK,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACxC,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,MAAM,cAAc,GAAG,aAAa,CAAC;QACrC,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CACb,iJAAiJ,CAClJ,CAAC;QACJ,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACxC,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE,CAChD,cAAc,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CACzE,CAAC;QAEF,cAAc,CAAC,IAAI,EAAE,CAAC;QAEtB,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC;QAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtC,OAAO;YACL,GAAG,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACzC,GAAG,EAAE,GAAG;SACT,CAAC;IACJ,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,aAAa,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,WAAW,GAAG,MAAM,2BAA2B,CAAC,YAAY,CAAC,CAAC;QACpE,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,gBAAgB,CAAC,OAAO;gBAC3B,OAAO;oBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;oBAChC,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,IAAI;iBACd,CAAC;YACJ,KAAK,gBAAgB,CAAC,MAAM;gBAC1B,OAAO;oBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;oBAC/B,OAAO,EAAE,OAAO;oBAChB,WAAW,EAAE,IAAI;oBACjB,OAAO,EAAE,KAAK;iBACf,CAAC;YACJ;gBACE,OAAO,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IACD,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACpD,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;gBAChC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus } from 'expo-modules-core';\nimport { DeviceEventEmitter } from 'react-native';\n\nimport type { AVPlaybackNativeSource, AVPlaybackStatus, AVPlaybackStatusToSet } from './AV.types';\nimport type { RecordingStatus } from './Audio/Recording.types';\nimport { RecordingOptionsPresets } from './Audio/RecordingConstants';\n\nasync function getPermissionWithQueryAsync(\n  name: PermissionNameWithAdditionalValues\n): Promise<PermissionStatus | null> {\n  if (!navigator || !navigator.permissions || !navigator.permissions.query) return null;\n\n  try {\n    const { state } = await navigator.permissions.query({ name });\n    switch (state) {\n      case 'granted':\n        return PermissionStatus.GRANTED;\n      case 'denied':\n        return PermissionStatus.DENIED;\n      default:\n        return PermissionStatus.UNDETERMINED;\n    }\n  } catch {\n    // Firefox - TypeError: 'microphone' (value of 'name' member of PermissionDescriptor) is not a valid value for enumeration PermissionName.\n    return PermissionStatus.UNDETERMINED;\n  }\n}\n\nfunction getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    // TODO(@kitten): The types indicates that this is incorrect.\n    // Please check whether this is correct!\n    // @ts-expect-error: The `successCallback` doesn't match a `resolve` function\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nfunction getStatusFromMedia(media?: HTMLMediaElement): AVPlaybackStatus {\n  if (!media) {\n    return {\n      isLoaded: false,\n      error: undefined,\n    };\n  }\n\n  const isPlaying = !!(\n    media.currentTime > 0 &&\n    !media.paused &&\n    !media.ended &&\n    media.readyState > 2\n  );\n\n  const status: AVPlaybackStatus = {\n    isLoaded: true,\n    uri: media.src,\n    progressUpdateIntervalMillis: 100, //TODO: Bacon: Add interval between calls\n    durationMillis: media.duration * 1000,\n    positionMillis: media.currentTime * 1000,\n    // playableDurationMillis: media.buffered * 1000,\n    // seekMillisToleranceBefore?: number\n    // seekMillisToleranceAfter?: number\n    shouldPlay: media.autoplay,\n    isPlaying,\n    isBuffering: false, //media.waiting,\n    rate: media.playbackRate,\n    // TODO: Bacon: This seems too complicated right now: https://webaudio.github.io/web-audio-api/#dom-biquadfilternode-frequency\n    shouldCorrectPitch: false,\n    volume: media.volume,\n    audioPan: 0,\n    isMuted: media.muted,\n    isLooping: media.loop,\n    didJustFinish: media.ended,\n  };\n\n  return status;\n}\n\nasync function setStatusForMedia(\n  media: HTMLMediaElement,\n  status: AVPlaybackStatusToSet\n): Promise<AVPlaybackStatus> {\n  if (status.positionMillis !== undefined) {\n    media.currentTime = status.positionMillis / 1000;\n  }\n  // if (status.progressUpdateIntervalMillis !== undefined) {\n  //   media.progressUpdateIntervalMillis = status.progressUpdateIntervalMillis;\n  // }\n  // if (status.seekMillisToleranceBefore !== undefined) {\n  //   media.seekMillisToleranceBefore = status.seekMillisToleranceBefore;\n  // }\n  // if (status.seekMillisToleranceAfter !== undefined) {\n  //   media.seekMillisToleranceAfter = status.seekMillisToleranceAfter;\n  // }\n  // if (status.shouldCorrectPitch !== undefined) {\n  //   media.shouldCorrectPitch = status.shouldCorrectPitch;\n  // }\n  if (status.shouldPlay !== undefined) {\n    if (status.shouldPlay) {\n      await media.play();\n    } else {\n      await media.pause();\n    }\n  }\n  if (status.rate !== undefined) {\n    media.playbackRate = status.rate;\n  }\n  if (status.shouldCorrectPitch !== undefined) {\n    media.preservesPitch = status.shouldCorrectPitch;\n  }\n  if (status.volume !== undefined) {\n    media.volume = status.volume;\n  }\n  if (status.isMuted !== undefined) {\n    media.muted = status.isMuted;\n  }\n  if (status.isLooping !== undefined) {\n    media.loop = status.isLooping;\n  }\n\n  return getStatusFromMedia(media);\n}\n\nlet mediaRecorder: null | MediaRecorder = null;\nlet mediaRecorderUptimeOfLastStartResume: number = 0;\nlet mediaRecorderDurationAlreadyRecorded: number = 0;\nlet mediaRecorderIsRecording: boolean = false;\n\nfunction getAudioRecorderDurationMillis() {\n  let duration = mediaRecorderDurationAlreadyRecorded;\n  if (mediaRecorderIsRecording && mediaRecorderUptimeOfLastStartResume > 0) {\n    duration += Date.now() - mediaRecorderUptimeOfLastStartResume;\n  }\n  return duration;\n}\n\nexport default {\n  async getStatusForVideo(element: HTMLMediaElement): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async loadForVideo(\n    element: HTMLMediaElement,\n    nativeSource: AVPlaybackNativeSource,\n    fullInitialStatus: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async unloadForVideo(element: HTMLMediaElement): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async setStatusForVideo(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  async replayVideo(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  /* Audio */\n  async setAudioMode() {},\n  async setAudioIsEnabled() {},\n  async getStatusForSound(element: HTMLMediaElement) {\n    return getStatusFromMedia(element);\n  },\n  async loadForSound(\n    nativeSource: string | { uri: string; [key: string]: any },\n    fullInitialStatus: AVPlaybackStatusToSet\n  ): Promise<[HTMLMediaElement, AVPlaybackStatus]> {\n    const source = typeof nativeSource === 'string' ? nativeSource : nativeSource.uri;\n    const media = new Audio(source);\n\n    media.ontimeupdate = () => {\n      DeviceEventEmitter.emit('didUpdatePlaybackStatus', {\n        key: media,\n        status: getStatusFromMedia(media),\n      });\n    };\n\n    media.onerror = () => {\n      DeviceEventEmitter.emit('ExponentAV.onError', {\n        key: media,\n        error: media.error!.message,\n      });\n    };\n\n    const status = await setStatusForMedia(media, fullInitialStatus);\n\n    return [media, status];\n  },\n  async unloadForSound(element: HTMLMediaElement) {\n    element.pause();\n    element.removeAttribute('src');\n    element.load();\n    return getStatusFromMedia(element);\n  },\n  async setStatusForSound(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  async replaySound(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n\n  /* Recording */\n  //   async setUnloadedCallbackForAndroidRecording() {},\n  async getAudioRecordingStatus(): Promise<RecordingStatus> {\n    return {\n      canRecord: mediaRecorder?.state === 'recording' || mediaRecorder?.state === 'inactive',\n      isRecording: mediaRecorder?.state === 'recording',\n      isDoneRecording: false,\n      durationMillis: getAudioRecorderDurationMillis(),\n      uri: null,\n    };\n  },\n  // TODO(@kitten): Needs to be typed\n  async prepareAudioRecorder(options: any): Promise<{\n    uri: string | null;\n    // status is of type RecordingStatus, but without the canRecord field populated\n    status: Pick<RecordingStatus, Exclude<keyof RecordingStatus, 'canRecord'>>;\n  }> {\n    if (typeof navigator !== 'undefined' && !navigator.mediaDevices) {\n      throw new Error('No media devices available');\n    }\n\n    mediaRecorderUptimeOfLastStartResume = 0;\n    mediaRecorderDurationAlreadyRecorded = 0;\n\n    const stream = await getUserMedia({ audio: true });\n\n    mediaRecorder = new window.MediaRecorder(\n      stream,\n      options?.web || RecordingOptionsPresets.HIGH_QUALITY.web\n    );\n\n    mediaRecorder.addEventListener('pause', () => {\n      mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n      mediaRecorderIsRecording = false;\n    });\n\n    mediaRecorder.addEventListener('resume', () => {\n      mediaRecorderUptimeOfLastStartResume = Date.now();\n      mediaRecorderIsRecording = true;\n    });\n\n    mediaRecorder.addEventListener('start', () => {\n      mediaRecorderUptimeOfLastStartResume = Date.now();\n      mediaRecorderDurationAlreadyRecorded = 0;\n      mediaRecorderIsRecording = true;\n    });\n\n    mediaRecorder.addEventListener('stop', () => {\n      mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n      mediaRecorderIsRecording = false;\n\n      // Clears recording icon in Chrome tab\n      stream.getTracks().forEach((track) => track.stop());\n    });\n\n    const { uri, ...status } = await this.getAudioRecordingStatus();\n\n    return { uri: null, status };\n  },\n  async startAudioRecording(): Promise<RecordingStatus> {\n    if (mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    if (mediaRecorder.state === 'paused') {\n      mediaRecorder.resume();\n    } else {\n      mediaRecorder.start();\n    }\n\n    return this.getAudioRecordingStatus();\n  },\n  async pauseAudioRecording(): Promise<RecordingStatus> {\n    if (mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    // Set status to paused\n    mediaRecorder.pause();\n\n    return this.getAudioRecordingStatus();\n  },\n  async stopAudioRecording(): Promise<RecordingStatus> {\n    const _mediaRecorder = mediaRecorder;\n    if (_mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    if (_mediaRecorder.state === 'inactive') {\n      return this.getAudioRecordingStatus();\n    }\n\n    const dataPromise = new Promise<Blob>((resolve) =>\n      _mediaRecorder.addEventListener('dataavailable', (e) => resolve(e.data))\n    );\n\n    _mediaRecorder.stop();\n\n    const data = await dataPromise;\n    const url = URL.createObjectURL(data);\n\n    return {\n      ...(await this.getAudioRecordingStatus()),\n      uri: url,\n    };\n  },\n  async unloadAudioRecorder(): Promise<void> {\n    mediaRecorder = null;\n  },\n\n  async getPermissionsAsync(): Promise<PermissionResponse> {\n    const maybeStatus = await getPermissionWithQueryAsync('microphone');\n    switch (maybeStatus) {\n      case PermissionStatus.GRANTED:\n        return {\n          status: PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true,\n        };\n      case PermissionStatus.DENIED:\n        return {\n          status: PermissionStatus.DENIED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false,\n        };\n      default:\n        return await this.requestPermissionsAsync();\n    }\n  },\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    try {\n      const stream = await getUserMedia({ audio: true });\n      stream.getTracks().forEach((track) => track.stop());\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    } catch {\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    }\n  },\n};\n"]}