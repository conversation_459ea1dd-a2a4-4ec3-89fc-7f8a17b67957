/**
 * This module was automatically generated by `ts-interface-builder`
 */
import * as t from "ts-interface-checker";
// tslint:disable:object-literal-key-quotes

export const IH265TranscoderObserver = t.iface([], {
  "onEnableTranscode": t.opt(t.func("void", t.param("result", "H265TranscodeResult"))),
  "onQueryChannel": t.opt(t.func("void", t.param("result", "H265TranscodeResult"), t.param("originChannel", "string"), t.param("transcodeChannel", "string"))),
  "onTriggerTranscode": t.opt(t.func("void", t.param("result", "H265TranscodeResult"))),
});

const exportedTypeSuite: t.ITypeSuite = {
  IH265TranscoderObserver,
};
export default exportedTypeSuite;
