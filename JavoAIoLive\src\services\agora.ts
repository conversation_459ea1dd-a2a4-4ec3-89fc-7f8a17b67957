// Agora service implementation for live streaming
import Rtc<PERSON>ngine, {
  RtcEngineContext,
  ChannelProfileType,
  ClientRoleType,
  VideoEncoderConfiguration,
  VideoEncoderConfigurationPreset,
  IRtcEngineEventHandler,
  ConnectionStateType,
  ConnectionChangedReasonType,
  UserOfflineReasonType,
} from 'react-native-agora';

import { AGORA_CONFIG } from '../utils/constants';

export interface AgoraEventHandlers {
  onJoinChannelSuccess?: (channel: string, uid: number, elapsed: number) => void;
  onUserJoined?: (uid: number, elapsed: number) => void;
  onUserOffline?: (uid: number, reason: UserOfflineReasonType) => void;
  onConnectionStateChanged?: (state: ConnectionStateType, reason: ConnectionChangedReasonType) => void;
  onError?: (errorCode: number) => void;
  onWarning?: (warningCode: number) => void;
}

export class AgoraService {
  static engine: RtcEngine | null = null;
  static initialized = false;
  static isInChannel = false;
  static currentChannelName = '';
  static currentUid = 0;

  static async initialize(eventHandlers?: AgoraEventHandlers) {
    if (this.initialized && this.engine) return this.engine;

    try {
      console.log('Initializing Agora RTC Engine...');
      console.log('App ID:', AGORA_CONFIG.appId);

      if (!AGORA_CONFIG.appId) {
        throw new Error('Agora App ID is required. Please set EXPO_PUBLIC_AGORA_APP_ID in your environment.');
      }

      // Create RTC Engine context
      const context: RtcEngineContext = {
        appId: AGORA_CONFIG.appId,
        // Enable audio and video modules
        areaCode: 1, // Global area code
      };

      // Create engine instance
      this.engine = await RtcEngine.createWithContext(context);

      // Set up event handlers
      if (eventHandlers) {
        this.setupEventHandlers(eventHandlers);
      }

      // Enable video module
      await this.engine.enableVideo();

      // Enable audio module
      await this.engine.enableAudio();

      // Set channel profile to live broadcasting
      await this.engine.setChannelProfile(ChannelProfileType.ChannelProfileLiveBroadcasting);

      // Set video encoder configuration
      const videoConfig: VideoEncoderConfiguration = {
        dimensions: { width: 640, height: 480 },
        frameRate: 15,
        bitrate: 400,
        orientationMode: 0,
        degradationPreference: 0,
        mirrorMode: 0,
      };
      await this.engine.setVideoEncoderConfiguration(videoConfig);

      this.initialized = true;
      console.log('Agora RTC Engine initialized successfully');

      return this.engine;
    } catch (error) {
      console.error('Failed to initialize Agora RTC Engine:', error);
      throw error;
    }
  }

  static setupEventHandlers(handlers: AgoraEventHandlers) {
    if (!this.engine) return;

    const eventHandler: IRtcEngineEventHandler = {
      onJoinChannelSuccess: (channel, uid, elapsed) => {
        console.log('Join channel success:', { channel, uid, elapsed });
        this.isInChannel = true;
        this.currentChannelName = channel;
        this.currentUid = uid;
        handlers.onJoinChannelSuccess?.(channel, uid, elapsed);
      },

      onUserJoined: (uid, elapsed) => {
        console.log('User joined:', { uid, elapsed });
        handlers.onUserJoined?.(uid, elapsed);
      },

      onUserOffline: (uid, reason) => {
        console.log('User offline:', { uid, reason });
        handlers.onUserOffline?.(uid, reason);
      },

      onConnectionStateChanged: (state, reason) => {
        console.log('Connection state changed:', { state, reason });
        handlers.onConnectionStateChanged?.(state, reason);
      },

      onError: (errorCode) => {
        console.error('Agora error:', errorCode);
        handlers.onError?.(errorCode);
      },

      onWarning: (warningCode) => {
        console.warn('Agora warning:', warningCode);
        handlers.onWarning?.(warningCode);
      },
    };

    this.engine.addListener('onJoinChannelSuccess', eventHandler.onJoinChannelSuccess!);
    this.engine.addListener('onUserJoined', eventHandler.onUserJoined!);
    this.engine.addListener('onUserOffline', eventHandler.onUserOffline!);
    this.engine.addListener('onConnectionStateChanged', eventHandler.onConnectionStateChanged!);
    this.engine.addListener('onError', eventHandler.onError!);
    this.engine.addListener('onWarning', eventHandler.onWarning!);
  }

  static async joinChannel(channelName: string, token: string | null, uid: number, isBroadcaster: boolean = true) {
    if (!this.engine) {
      throw new Error('Agora engine not initialized. Call initialize() first.');
    }

    try {
      console.log('Joining channel:', { channelName, uid, isBroadcaster });

      // Set client role
      const clientRole = isBroadcaster ? ClientRoleType.ClientRoleBroadcaster : ClientRoleType.ClientRoleAudience;
      await this.engine.setClientRole(clientRole);

      // Join channel
      await this.engine.joinChannel(token, channelName, uid);

      console.log('Successfully joined channel');
    } catch (error) {
      console.error('Failed to join channel:', error);
      throw error;
    }
  }

  static async leaveChannel() {
    if (!this.engine || !this.isInChannel) return;

    try {
      console.log('Leaving channel...');
      await this.engine.leaveChannel();
      this.isInChannel = false;
      this.currentChannelName = '';
      this.currentUid = 0;
      console.log('Successfully left channel');
    } catch (error) {
      console.error('Failed to leave channel:', error);
      throw error;
    }
  }

  static async startPreview() {
    if (!this.engine) {
      throw new Error('Agora engine not initialized');
    }

    try {
      console.log('Starting camera preview...');
      await this.engine.startPreview();
      console.log('Camera preview started');
    } catch (error) {
      console.error('Failed to start preview:', error);
      throw error;
    }
  }

  static async stopPreview() {
    if (!this.engine) return;

    try {
      console.log('Stopping camera preview...');
      await this.engine.stopPreview();
      console.log('Camera preview stopped');
    } catch (error) {
      console.error('Failed to stop preview:', error);
      throw error;
    }
  }

  static async enableVideo() {
    if (!this.engine) return;

    try {
      await this.engine.enableLocalVideo(true);
      console.log('Video enabled');
    } catch (error) {
      console.error('Failed to enable video:', error);
      throw error;
    }
  }

  static async disableVideo() {
    if (!this.engine) return;

    try {
      await this.engine.enableLocalVideo(false);
      console.log('Video disabled');
    } catch (error) {
      console.error('Failed to disable video:', error);
      throw error;
    }
  }

  static async enableAudio() {
    if (!this.engine) return;

    try {
      await this.engine.enableLocalAudio(true);
      console.log('Audio enabled');
    } catch (error) {
      console.error('Failed to enable audio:', error);
      throw error;
    }
  }

  static async disableAudio() {
    if (!this.engine) return;

    try {
      await this.engine.enableLocalAudio(false);
      console.log('Audio disabled');
    } catch (error) {
      console.error('Failed to disable audio:', error);
      throw error;
    }
  }

  static async switchCamera() {
    if (!this.engine) return;

    try {
      await this.engine.switchCamera();
      console.log('Camera switched');
    } catch (error) {
      console.error('Failed to switch camera:', error);
      throw error;
    }
  }

  static async muteLocalAudio(muted: boolean) {
    if (!this.engine) return;

    try {
      await this.engine.muteLocalAudioStream(muted);
      console.log(`Audio ${muted ? 'muted' : 'unmuted'}`);
    } catch (error) {
      console.error('Failed to mute/unmute audio:', error);
      throw error;
    }
  }

  static async muteLocalVideo(muted: boolean) {
    if (!this.engine) return;

    try {
      await this.engine.muteLocalVideoStream(muted);
      console.log(`Video ${muted ? 'muted' : 'unmuted'}`);
    } catch (error) {
      console.error('Failed to mute/unmute video:', error);
      throw error;
    }
  }

  static async generateToken(channelName: string, uid: number): Promise<string | null> {
    // For development, we can use null token (less secure)
    // In production, this should call your backend to generate a token
    console.log('Generating token for:', { channelName, uid });

    // TODO: Implement backend token generation
    // For now, return null for testing (works in development mode)
    return null;
  }

  static async destroy() {
    if (!this.engine) return;

    try {
      console.log('Destroying Agora engine...');

      if (this.isInChannel) {
        await this.leaveChannel();
      }

      await this.stopPreview();
      await this.engine.destroy();

      this.engine = null;
      this.initialized = false;
      this.isInChannel = false;
      this.currentChannelName = '';
      this.currentUid = 0;

      console.log('Agora engine destroyed');
    } catch (error) {
      console.error('Failed to destroy Agora engine:', error);
      throw error;
    }
  }

  static getEngine() {
    return this.engine;
  }

  static isInitialized() {
    return this.initialized && this.engine !== null;
  }

  static getChannelInfo() {
    return {
      isInChannel: this.isInChannel,
      channelName: this.currentChannelName,
      uid: this.currentUid,
    };
  }
}

export default AgoraService;
