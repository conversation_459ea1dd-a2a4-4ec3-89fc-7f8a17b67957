// Camera and microphone permissions service
import { Camera } from 'expo-camera';
import { Audio } from 'expo-av';
import { Alert, Linking, Platform } from 'react-native';

export interface PermissionStatus {
  camera: boolean;
  microphone: boolean;
  granted: boolean;
}

export class PermissionsService {
  static async requestCameraPermission(): Promise<boolean> {
    try {
      console.log('Requesting camera permission...');
      const { status } = await Camera.requestCameraPermissionsAsync();
      
      if (status === 'granted') {
        console.log('Camera permission granted');
        return true;
      } else {
        console.log('Camera permission denied');
        this.showPermissionDeniedAlert('Camera', 'live streaming');
        return false;
      }
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return false;
    }
  }

  static async requestMicrophonePermission(): Promise<boolean> {
    try {
      console.log('Requesting microphone permission...');
      const { status } = await Audio.requestPermissionsAsync();
      
      if (status === 'granted') {
        console.log('Microphone permission granted');
        return true;
      } else {
        console.log('Microphone permission denied');
        this.showPermissionDeniedAlert('Microphone', 'audio streaming');
        return false;
      }
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      return false;
    }
  }

  static async requestAllPermissions(): Promise<PermissionStatus> {
    console.log('Requesting all required permissions...');
    
    const [cameraGranted, microphoneGranted] = await Promise.all([
      this.requestCameraPermission(),
      this.requestMicrophonePermission(),
    ]);

    const result: PermissionStatus = {
      camera: cameraGranted,
      microphone: microphoneGranted,
      granted: cameraGranted && microphoneGranted,
    };

    console.log('Permission status:', result);
    return result;
  }

  static async checkCameraPermission(): Promise<boolean> {
    try {
      const { status } = await Camera.getCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking camera permission:', error);
      return false;
    }
  }

  static async checkMicrophonePermission(): Promise<boolean> {
    try {
      const { status } = await Audio.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return false;
    }
  }

  static async checkAllPermissions(): Promise<PermissionStatus> {
    console.log('Checking all permissions...');
    
    const [cameraGranted, microphoneGranted] = await Promise.all([
      this.checkCameraPermission(),
      this.checkMicrophonePermission(),
    ]);

    const result: PermissionStatus = {
      camera: cameraGranted,
      microphone: microphoneGranted,
      granted: cameraGranted && microphoneGranted,
    };

    console.log('Current permission status:', result);
    return result;
  }

  static showPermissionDeniedAlert(permissionType: string, purpose: string) {
    Alert.alert(
      `${permissionType} Permission Required`,
      `JavoAIo Live needs access to your ${permissionType.toLowerCase()} for ${purpose}. Please grant permission in your device settings.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Open Settings',
          onPress: () => {
            if (Platform.OS === 'ios') {
              Linking.openURL('app-settings:');
            } else {
              Linking.openSettings();
            }
          },
        },
      ]
    );
  }

  static showPermissionsRequiredAlert() {
    Alert.alert(
      'Permissions Required',
      'Camera and microphone permissions are required for live streaming. Please grant these permissions to continue.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Grant Permissions',
          onPress: async () => {
            await this.requestAllPermissions();
          },
        },
      ]
    );
  }

  static async ensurePermissions(): Promise<boolean> {
    const permissions = await this.checkAllPermissions();
    
    if (permissions.granted) {
      return true;
    }

    // Request missing permissions
    const newPermissions = await this.requestAllPermissions();
    return newPermissions.granted;
  }
}

export default PermissionsService;
