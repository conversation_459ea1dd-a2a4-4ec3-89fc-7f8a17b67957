/**
 * @license React
 * react-refresh-babel.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';module.exports=function(r){function t(a,b){var c=a.scope.generateUidIdentifier("c");l.has(a)||l.set(a,[]);l.get(a).push({handle:c,persistentID:b});return c}function u(a){return"string"===typeof a&&"A"<=a[0]&&"Z">=a[0]}function m(a,b,c){var d=b.node;switch(d.type){case "Identifier":if(!u(d.name))break;c(a,d,null);return!0;case "FunctionDeclaration":return c(a,d.id,null),!0;case "ArrowFunctionExpression":if("ArrowFunctionExpression"===d.body.type)break;c(a,d,b);return!0;case "FunctionExpression":return c(a,
d,b),!0;case "CallExpression":var e=b.get("arguments");if(void 0===e||0===e.length)break;var g=b.get("callee");switch(g.node.type){case "MemberExpression":case "Identifier":g=g.getSource();if(!m(a+"$"+g,e[0],c))return!1;c(a,d,b);return!0;default:return!1}case "VariableDeclarator":if(e=d.init,null!==e&&(g=d.id.name,u(g))){switch(e.type){case "ArrowFunctionExpression":case "FunctionExpression":break;case "CallExpression":d=e.callee;var f=d.type;if("Import"===f||"Identifier"===f&&(0===d.name.indexOf("require")||
0===d.name.indexOf("import")))return!1;break;case "TaggedTemplateExpression":break;default:return!1}d=b.get("init");if(m(a,d,c))return!0;g=b.scope.getBinding(g);if(void 0===g)return;b=!1;g=g.referencePaths;for(f=0;f<g.length;f++){var h=g[f];if(!h.node||"JSXIdentifier"===h.node.type||"Identifier"===h.node.type){h=h.parent;if("JSXOpeningElement"===h.type)b=!0;else if("CallExpression"===h.type){h=h.callee;var v=void 0;switch(h.type){case "Identifier":v=h.name;break;case "MemberExpression":v=h.property.name}switch(v){case "createElement":case "jsx":case "jsxDEV":case "jsxs":b=
!0}}if(b)return c(a,e,d),!0}}}}return!1}function x(a){a=n.get(a);return void 0===a?null:{key:a.map(function(a){return a.name+"{"+a.key+"}"}).join("\n"),customHooks:a.filter(function(a){a:switch(a.name){case "useState":case "React.useState":case "useReducer":case "React.useReducer":case "useEffect":case "React.useEffect":case "useLayoutEffect":case "React.useLayoutEffect":case "useMemo":case "React.useMemo":case "useCallback":case "React.useCallback":case "useRef":case "React.useRef":case "useContext":case "React.useContext":case "useImperativeHandle":case "React.useImperativeHandle":case "useDebugValue":case "React.useDebugValue":a=
!0;break a;default:a=!1}return!a}).map(function(a){return f.cloneDeep(a.callee)})}}function C(a){a=a.hub.file;var b=y.get(a);if(void 0!==b)return b;b=!1;for(var c=a.ast.comments,d=0;d<c.length;d++)if(-1!==c[d].value.indexOf("@refresh reset")){b=!0;break}y.set(a,b);return b}function w(a,b,c){var d=b.key;b=b.customHooks;var e=C(c.path),g=[];b.forEach(function(a){switch(a.type){case "MemberExpression":if("Identifier"===a.object.type)var b=a.object.name;break;case "Identifier":b=a.name}c.hasBinding(b)?
g.push(a):e=!0});b=d;"function"!==typeof require||p.emitFullSignatures||(b=require("crypto").createHash("sha1").update(d).digest("base64"));a=[a,f.stringLiteral(b)];(e||0<g.length)&&a.push(f.booleanLiteral(e));0<g.length&&a.push(f.functionExpression(null,[],f.blockStatement([f.returnStatement(f.arrayExpression(g))])));return a}function D(a){for(var b=[];;){if(!a)return b;var c=a.parentPath;if(!c)return b;if("AssignmentExpression"===c.node.type&&a.node===c.node.right)a=c;else if("CallExpression"===
c.node.type&&a.node!==c.node.callee)b.push(c),a=c;else return b}}var p=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if("function"===typeof r.env){var z=r.env();if("development"!==z&&!p.skipEnvCheck)throw Error('React Refresh Babel transform should only be enabled in development environment. Instead, the environment is: "'+z+'". If you want to override this check, pass {skipEnvCheck: true} as plugin options.');}var f=r.types,E=f.identifier(p.refreshReg||"$RefreshReg$"),A=f.identifier(p.refreshSig||
"$RefreshSig$"),l=new Map,y=new WeakMap,k=new WeakSet,q=new WeakSet,B=new WeakSet,n=new WeakMap,F={CallExpression:function(a){var b=a.node.callee,c=null;switch(b.type){case "Identifier":c=b.name;break;case "MemberExpression":c=b.property.name}if(null!==c&&/^use[A-Z]/.test(c)&&(b=a.scope.getFunctionParent(),null!==b)){b=b.block;n.has(b)||n.set(b,[]);b=n.get(b);var d="";"VariableDeclarator"===a.parent.type&&(d=a.parentPath.get("id").getSource());var e=a.get("arguments");"useState"===c&&0<e.length?d+=
"("+e[0].getSource()+")":"useReducer"===c&&1<e.length&&(d+="("+e[1].getSource()+")");b.push({callee:a.node.callee,name:c,key:d})}}};return{visitor:{ExportDefaultDeclaration:function(a){var b=a.node,c=b.declaration,d=a.get("declaration");if("CallExpression"===c.type&&!k.has(b)){k.add(b);var e=a.parentPath;m("%default%",d,function(a,b,d){null!==d&&(a=t(e,a),d.replaceWith(f.assignmentExpression("=",a,b)))})}},FunctionDeclaration:{enter:function(a){var b=a.node,c="";switch(a.parent.type){case "Program":var d=
a;var e=a.parentPath;break;case "TSModuleBlock":d=a;e=d.parentPath.parentPath;break;case "ExportNamedDeclaration":d=a.parentPath;e=d.parentPath;break;case "ExportDefaultDeclaration":d=a.parentPath;e=d.parentPath;break;default:return}if("TSModuleBlock"===a.parent.type||"ExportNamedDeclaration"===a.parent.type)for(;"Program"!==e.type;){if("TSModuleDeclaration"===e.type){if("Program"!==e.parentPath.type&&"ExportNamedDeclaration"!==e.parentPath.type)return;c=e.node.id.name+"$"+c}e=e.parentPath}var g=
b.id;null!==g&&(g=g.name,u(g)&&!k.has(b)&&(k.add(b),m(c+g,a,function(a,b){a=t(e,a);d.insertAfter(f.expressionStatement(f.assignmentExpression("=",a,b)))})))},exit:function(a){var b=a.node,c=b.id;if(null!==c){var d=x(b);if(null!==d&&!q.has(b)){q.add(b);b=a.scope.generateUidIdentifier("_s");a.scope.parent.push({id:b,init:f.callExpression(A,[])});a.get("body").unshiftContainer("body",f.expressionStatement(f.callExpression(b,[])));var e=null;a.find(function(a){if(a.parentPath.isBlock())return e=a,!0});
null!==e&&e.insertAfter(f.expressionStatement(f.callExpression(b,w(c,d,e.scope))))}}}},"ArrowFunctionExpression|FunctionExpression":{exit:function(a){var b=a.node,c=x(b);if(null!==c&&!q.has(b)){q.add(b);var d=a.scope.generateUidIdentifier("_s");a.scope.parent.push({id:d,init:f.callExpression(A,[])});"BlockStatement"!==a.node.body.type&&(a.node.body=f.blockStatement([f.returnStatement(a.node.body)]));a.get("body").unshiftContainer("body",f.expressionStatement(f.callExpression(d,[])));if("VariableDeclarator"===
a.parent.type){var e=null;a.find(function(a){if(a.parentPath.isBlock())return e=a,!0});null!==e&&e.insertAfter(f.expressionStatement(f.callExpression(d,w(a.parent.id,c,e.scope))))}else[a].concat(D(a)).forEach(function(a){a.replaceWith(f.callExpression(d,w(a.node,c,a.scope)))})}}},VariableDeclaration:function(a){var b=a.node,c="";switch(a.parent.type){case "Program":var d=a;var e=a.parentPath;break;case "TSModuleBlock":d=a;e=d.parentPath.parentPath;break;case "ExportNamedDeclaration":d=a.parentPath;
e=d.parentPath;break;case "ExportDefaultDeclaration":d=a.parentPath;e=d.parentPath;break;default:return}if("TSModuleBlock"===a.parent.type||"ExportNamedDeclaration"===a.parent.type)for(;"Program"!==e.type;){if("TSModuleDeclaration"===e.type){if("Program"!==e.parentPath.type&&"ExportNamedDeclaration"!==e.parentPath.type)return;c=e.node.id.name+"$"+c}e=e.parentPath}if(!k.has(b)&&(k.add(b),a=a.get("declarations"),1===a.length)){var g=a[0];m(c+g.node.id.name,g,function(a,b,c){null!==c&&(a=t(e,a),"VariableDeclarator"===
c.parent.type?d.insertAfter(f.expressionStatement(f.assignmentExpression("=",a,g.node.id))):c.replaceWith(f.assignmentExpression("=",a,b)))})}},Program:{enter:function(a){a.traverse(F)},exit:function(a){var b=l.get(a);if(void 0!==b){var c=a.node;if(!B.has(c)){B.add(c);l.delete(a);var d=[];a.pushContainer("body",f.variableDeclaration("var",d));b.forEach(function(b){var c=b.handle;a.pushContainer("body",f.expressionStatement(f.callExpression(E,[c,f.stringLiteral(b.persistentID)])));d.push(f.variableDeclarator(c))})}}}}}}};
