import React from 'react';
import { CheckBoxProps } from '../checkbox/CheckBox';
declare const _default: React.FunctionComponent<Omit<import("react-native").TouchableOpacityProps & import("../checkbox/CheckBoxIcon").CheckBoxIconProps & {
    Component?: typeof React.Component;
    iconRight?: boolean;
    title?: string | React.ReactElement<{}, string | React.JSXElementConstructor<any>>;
    titleProps?: import("react-native").TextProps;
    center?: boolean;
    right?: boolean;
    containerStyle?: import("react-native").StyleProp<import("react-native").ViewStyle>;
    wrapperStyle?: import("react-native").StyleProp<import("react-native").ViewStyle>;
    textStyle?: import("react-native").StyleProp<import("react-native").TextStyle>;
    checkedTitle?: string;
    fontFamily?: string;
} & Partial<import("../config").ThemeProps<CheckBoxProps>>, keyof import("../config").ThemeProps<T>>> | React.ForwardRefExoticComponent<import("react-native").TouchableOpacityProps & import("../checkbox/CheckBoxIcon").CheckBoxIconProps & {
    Component?: typeof React.Component;
    iconRight?: boolean;
    title?: string | React.ReactElement<{}, string | React.JSXElementConstructor<any>>;
    titleProps?: import("react-native").TextProps;
    center?: boolean;
    right?: boolean;
    containerStyle?: import("react-native").StyleProp<import("react-native").ViewStyle>;
    wrapperStyle?: import("react-native").StyleProp<import("react-native").ViewStyle>;
    textStyle?: import("react-native").StyleProp<import("react-native").TextStyle>;
    checkedTitle?: string;
    fontFamily?: string;
} & Partial<import("../config").ThemeProps<CheckBoxProps>>>;
export default _default;
