import { ChannelMediaRelayConfiguration, ConnectionStateType, DataStreamConfig, EncryptionConfig, LiveTranscoding, SimulcastConfig, SimulcastStreamConfig, SimulcastStreamMode, SpatialAudioParams, UserInfo, VideoCanvas, VideoEncoderConfiguration, VideoMirrorModeType, VideoStreamType, VideoSubscriptionOptions, WatermarkOptions } from '../AgoraBase';
import { ContentInspectConfig, RenderModeType, SnapshotConfig } from '../AgoraMediaBase';
import { ChannelMediaOptions, LeaveChannelOptions, StreamFallbackOptions } from '../IAgoraRtcEngine';
import { IRtcEngineEx, RtcConnection } from '../IAgoraRtcEngineEx';
import { IRtcEngineImpl } from './IAgoraRtcEngineImpl';
export declare class IRtcEngineExImpl extends IRtcEngineImpl implements IRtcEngineEx {
    joinChannelEx(token: string, connection: RtcConnection, options: ChannelMediaOptions): number;
    protected getApiTypeFromJoinChannelEx(token: string, connection: RtcConnection, options: ChannelMediaOptions): string;
    leaveChannelEx(connection: RtcConnection, options?: LeaveChannelOptions): number;
    protected getApiTypeFromLeaveChannelEx(connection: RtcConnection, options?: LeaveChannelOptions): string;
    leaveChannelWithUserAccountEx(channelId: string, userAccount: string, options?: LeaveChannelOptions): number;
    protected getApiTypeFromLeaveChannelWithUserAccountEx(channelId: string, userAccount: string, options?: LeaveChannelOptions): string;
    updateChannelMediaOptionsEx(options: ChannelMediaOptions, connection: RtcConnection): number;
    protected getApiTypeFromUpdateChannelMediaOptionsEx(options: ChannelMediaOptions, connection: RtcConnection): string;
    setVideoEncoderConfigurationEx(config: VideoEncoderConfiguration, connection: RtcConnection): number;
    protected getApiTypeFromSetVideoEncoderConfigurationEx(config: VideoEncoderConfiguration, connection: RtcConnection): string;
    setupRemoteVideoEx(canvas: VideoCanvas, connection: RtcConnection): number;
    protected getApiTypeFromSetupRemoteVideoEx(canvas: VideoCanvas, connection: RtcConnection): string;
    muteRemoteAudioStreamEx(uid: number, mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteRemoteAudioStreamEx(uid: number, mute: boolean, connection: RtcConnection): string;
    muteRemoteVideoStreamEx(uid: number, mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteRemoteVideoStreamEx(uid: number, mute: boolean, connection: RtcConnection): string;
    setRemoteVideoStreamTypeEx(uid: number, streamType: VideoStreamType, connection: RtcConnection): number;
    protected getApiTypeFromSetRemoteVideoStreamTypeEx(uid: number, streamType: VideoStreamType, connection: RtcConnection): string;
    muteLocalAudioStreamEx(mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteLocalAudioStreamEx(mute: boolean, connection: RtcConnection): string;
    muteLocalVideoStreamEx(mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteLocalVideoStreamEx(mute: boolean, connection: RtcConnection): string;
    muteAllRemoteAudioStreamsEx(mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteAllRemoteAudioStreamsEx(mute: boolean, connection: RtcConnection): string;
    muteAllRemoteVideoStreamsEx(mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteAllRemoteVideoStreamsEx(mute: boolean, connection: RtcConnection): string;
    setSubscribeAudioBlocklistEx(uidList: number[], uidNumber: number, connection: RtcConnection): number;
    protected getApiTypeFromSetSubscribeAudioBlocklistEx(uidList: number[], uidNumber: number, connection: RtcConnection): string;
    setSubscribeAudioAllowlistEx(uidList: number[], uidNumber: number, connection: RtcConnection): number;
    protected getApiTypeFromSetSubscribeAudioAllowlistEx(uidList: number[], uidNumber: number, connection: RtcConnection): string;
    setSubscribeVideoBlocklistEx(uidList: number[], uidNumber: number, connection: RtcConnection): number;
    protected getApiTypeFromSetSubscribeVideoBlocklistEx(uidList: number[], uidNumber: number, connection: RtcConnection): string;
    setSubscribeVideoAllowlistEx(uidList: number[], uidNumber: number, connection: RtcConnection): number;
    protected getApiTypeFromSetSubscribeVideoAllowlistEx(uidList: number[], uidNumber: number, connection: RtcConnection): string;
    setRemoteVideoSubscriptionOptionsEx(uid: number, options: VideoSubscriptionOptions, connection: RtcConnection): number;
    protected getApiTypeFromSetRemoteVideoSubscriptionOptionsEx(uid: number, options: VideoSubscriptionOptions, connection: RtcConnection): string;
    setRemoteVoicePositionEx(uid: number, pan: number, gain: number, connection: RtcConnection): number;
    protected getApiTypeFromSetRemoteVoicePositionEx(uid: number, pan: number, gain: number, connection: RtcConnection): string;
    setRemoteUserSpatialAudioParamsEx(uid: number, params: SpatialAudioParams, connection: RtcConnection): number;
    protected getApiTypeFromSetRemoteUserSpatialAudioParamsEx(uid: number, params: SpatialAudioParams, connection: RtcConnection): string;
    setRemoteRenderModeEx(uid: number, renderMode: RenderModeType, mirrorMode: VideoMirrorModeType, connection: RtcConnection): number;
    protected getApiTypeFromSetRemoteRenderModeEx(uid: number, renderMode: RenderModeType, mirrorMode: VideoMirrorModeType, connection: RtcConnection): string;
    enableLoopbackRecordingEx(connection: RtcConnection, enabled: boolean, deviceName?: string): number;
    protected getApiTypeFromEnableLoopbackRecordingEx(connection: RtcConnection, enabled: boolean, deviceName?: string): string;
    adjustRecordingSignalVolumeEx(volume: number, connection: RtcConnection): number;
    protected getApiTypeFromAdjustRecordingSignalVolumeEx(volume: number, connection: RtcConnection): string;
    muteRecordingSignalEx(mute: boolean, connection: RtcConnection): number;
    protected getApiTypeFromMuteRecordingSignalEx(mute: boolean, connection: RtcConnection): string;
    adjustUserPlaybackSignalVolumeEx(uid: number, volume: number, connection: RtcConnection): number;
    protected getApiTypeFromAdjustUserPlaybackSignalVolumeEx(uid: number, volume: number, connection: RtcConnection): string;
    getConnectionStateEx(connection: RtcConnection): ConnectionStateType;
    protected getApiTypeFromGetConnectionStateEx(connection: RtcConnection): string;
    enableEncryptionEx(connection: RtcConnection, enabled: boolean, config: EncryptionConfig): number;
    protected getApiTypeFromEnableEncryptionEx(connection: RtcConnection, enabled: boolean, config: EncryptionConfig): string;
    createDataStreamEx(config: DataStreamConfig, connection: RtcConnection): number;
    protected getApiTypeFromCreateDataStreamEx(config: DataStreamConfig, connection: RtcConnection): string;
    sendStreamMessageEx(streamId: number, data: Uint8Array, length: number, connection: RtcConnection): number;
    protected getApiTypeFromSendStreamMessageEx(streamId: number, data: Uint8Array, length: number, connection: RtcConnection): string;
    addVideoWatermarkEx(watermarkUrl: string, options: WatermarkOptions, connection: RtcConnection): number;
    protected getApiTypeFromAddVideoWatermarkEx(watermarkUrl: string, options: WatermarkOptions, connection: RtcConnection): string;
    clearVideoWatermarkEx(connection: RtcConnection): number;
    protected getApiTypeFromClearVideoWatermarkEx(connection: RtcConnection): string;
    sendCustomReportMessageEx(id: string, category: string, event: string, label: string, value: number, connection: RtcConnection): number;
    protected getApiTypeFromSendCustomReportMessageEx(id: string, category: string, event: string, label: string, value: number, connection: RtcConnection): string;
    enableAudioVolumeIndicationEx(interval: number, smooth: number, reportVad: boolean, connection: RtcConnection): number;
    protected getApiTypeFromEnableAudioVolumeIndicationEx(interval: number, smooth: number, reportVad: boolean, connection: RtcConnection): string;
    startRtmpStreamWithoutTranscodingEx(url: string, connection: RtcConnection): number;
    protected getApiTypeFromStartRtmpStreamWithoutTranscodingEx(url: string, connection: RtcConnection): string;
    startRtmpStreamWithTranscodingEx(url: string, transcoding: LiveTranscoding, connection: RtcConnection): number;
    protected getApiTypeFromStartRtmpStreamWithTranscodingEx(url: string, transcoding: LiveTranscoding, connection: RtcConnection): string;
    updateRtmpTranscodingEx(transcoding: LiveTranscoding, connection: RtcConnection): number;
    protected getApiTypeFromUpdateRtmpTranscodingEx(transcoding: LiveTranscoding, connection: RtcConnection): string;
    stopRtmpStreamEx(url: string, connection: RtcConnection): number;
    protected getApiTypeFromStopRtmpStreamEx(url: string, connection: RtcConnection): string;
    startOrUpdateChannelMediaRelayEx(configuration: ChannelMediaRelayConfiguration, connection: RtcConnection): number;
    protected getApiTypeFromStartOrUpdateChannelMediaRelayEx(configuration: ChannelMediaRelayConfiguration, connection: RtcConnection): string;
    stopChannelMediaRelayEx(connection: RtcConnection): number;
    protected getApiTypeFromStopChannelMediaRelayEx(connection: RtcConnection): string;
    pauseAllChannelMediaRelayEx(connection: RtcConnection): number;
    protected getApiTypeFromPauseAllChannelMediaRelayEx(connection: RtcConnection): string;
    resumeAllChannelMediaRelayEx(connection: RtcConnection): number;
    protected getApiTypeFromResumeAllChannelMediaRelayEx(connection: RtcConnection): string;
    getUserInfoByUserAccountEx(userAccount: string, connection: RtcConnection): UserInfo;
    protected getApiTypeFromGetUserInfoByUserAccountEx(userAccount: string, connection: RtcConnection): string;
    getUserInfoByUidEx(uid: number, connection: RtcConnection): UserInfo;
    protected getApiTypeFromGetUserInfoByUidEx(uid: number, connection: RtcConnection): string;
    enableDualStreamModeEx(enabled: boolean, streamConfig: SimulcastStreamConfig, connection: RtcConnection): number;
    protected getApiTypeFromEnableDualStreamModeEx(enabled: boolean, streamConfig: SimulcastStreamConfig, connection: RtcConnection): string;
    setDualStreamModeEx(mode: SimulcastStreamMode, streamConfig: SimulcastStreamConfig, connection: RtcConnection): number;
    protected getApiTypeFromSetDualStreamModeEx(mode: SimulcastStreamMode, streamConfig: SimulcastStreamConfig, connection: RtcConnection): string;
    setSimulcastConfigEx(simulcastConfig: SimulcastConfig, connection: RtcConnection): number;
    protected getApiTypeFromSetSimulcastConfigEx(simulcastConfig: SimulcastConfig, connection: RtcConnection): string;
    setHighPriorityUserListEx(uidList: number[], uidNum: number, option: StreamFallbackOptions, connection: RtcConnection): number;
    protected getApiTypeFromSetHighPriorityUserListEx(uidList: number[], uidNum: number, option: StreamFallbackOptions, connection: RtcConnection): string;
    takeSnapshotEx(connection: RtcConnection, uid: number, filePath: string): number;
    protected getApiTypeFromTakeSnapshotEx(connection: RtcConnection, uid: number, filePath: string): string;
    enableContentInspectEx(enabled: boolean, config: ContentInspectConfig, connection: RtcConnection): number;
    protected getApiTypeFromEnableContentInspectEx(enabled: boolean, config: ContentInspectConfig, connection: RtcConnection): string;
    startMediaRenderingTracingEx(connection: RtcConnection): number;
    protected getApiTypeFromStartMediaRenderingTracingEx(connection: RtcConnection): string;
    setParametersEx(connection: RtcConnection, parameters: string): number;
    protected getApiTypeFromSetParametersEx(connection: RtcConnection, parameters: string): string;
    getCallIdEx(connection: RtcConnection): string;
    protected getApiTypeFromGetCallIdEx(connection: RtcConnection): string;
    sendAudioMetadataEx(connection: RtcConnection, metadata: string, length: number): number;
    protected getApiTypeFromSendAudioMetadataEx(connection: RtcConnection, metadata: string, length: number): string;
    takeSnapshotWithConfigEx(connection: RtcConnection, uid: number, config: SnapshotConfig): number;
    protected getApiTypeFromTakeSnapshotWithConfigEx(connection: RtcConnection, uid: number, config: SnapshotConfig): string;
}
//# sourceMappingURL=IAgoraRtcEngineExImpl.d.ts.map