{"name": "terminal-link", "version": "2.1.1", "description": "Create clickable links in the terminal", "license": "MIT", "repository": "sindresorhus/terminal-link", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "devDependencies": {"ava": "^2.3.0", "clear-module": "^4.0.0", "tsd": "^0.11.0", "xo": "^0.25.3"}}