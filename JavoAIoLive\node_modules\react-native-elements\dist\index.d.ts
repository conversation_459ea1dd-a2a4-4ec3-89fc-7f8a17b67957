/// <reference types="react" />
import Button, { ButtonProps } from './buttons/Button';
import Chip, { ChipProps } from './buttons/Chip';
import Input, { InputProps } from './input/Input';
import Icon, { IconProps } from './icons/Icon';
import ListItem from './list/ListItem';
import { ListItemProps } from './list/ListItemBase';
import { ListItemAccordionProps } from './list/ListItemAccordion';
import { ListItemSwipeableProps } from './list/ListItemSwipeable';
import SocialIcon, { SocialIconProps } from './social/SocialIcon';
import Overlay, { OverlayProps } from './overlay/Overlay';
import SearchBar, { SearchBarProps } from './searchbar/SearchBar';
import { SearchBarAndroidProps } from './searchbar/SearchBar-android';
import { SearchBarDefaultProps } from './searchbar/SearchBar-default';
import { SearchBarIosProps } from './searchbar/SearchBar-ios';
import Badge, { BadgeProps } from './badge/Badge';
import withBadge from './badge/withBadge';
import CheckBox, { CheckBoxProps } from './checkbox/CheckBox';
import Divider, { DividerProps } from './divider/Divider';
import Slider, { SliderProps } from './slider/Slider';
import ButtonGroup, { ButtonGroupProps } from './buttons/ButtonGroup';
import Image, { ImageProps } from './image/Image';
import FAB, { FABProps } from './buttons/FAB';
import SpeedDial, { SpeedDialActionProps, SpeedDialProps } from './buttons/SpeedDial';
import Card, { CardProps } from './card/Card';
import Dialog, { DialogProps } from './dialog/Dialog';
import { DialogLoadingProps } from './dialog/DialogLoading';
import { DialogTitleProps } from './dialog/DialogTitle';
import Tile, { TileProps } from './tile/Tile';
import Avatar, { AvatarProps } from './avatar/Avatar';
import Accessory, { AccessoryProps } from './avatar/Accessory';
import Header, { HeaderProps } from './header/Header';
import PricingCard, { PricingCardProps } from './pricing/PricingCard';
import Tooltip, { TooltipProps } from './tooltip/Tooltip';
import BottomSheet, { BottomSheetProps } from './bottomSheet/BottomSheet';
import LinearProgress, { LinearProgressProps } from './linearProgress';
import Switch, { SwitchProps } from './switch/switch';
import Tab, { TabItemProps, TabProps } from './tab/Tab';
import TabView, { TabViewProps } from './tab/TabView';
import { TapRatingProps, SwipeRatingProps } from 'react-native-ratings';
import Text, { TextProps } from './text/Text';
import { Colors, colors, ThemeProvider, ThemeConsumer, ThemeContext, withTheme, makeStyles, useTheme, UpdateTheme, ReplaceTheme, FullTheme, Theme, ThemeProps } from './config';
import getIconType, { registerCustomIconType } from './helpers/getIconType';
import normalize from './helpers/normalizeText';
declare const AirbnbRating: import("react").FunctionComponent<Omit<TapRatingProps, keyof ThemeProps<T>>> | import("react").ForwardRefExoticComponent<TapRatingProps>;
declare const Rating: import("react").ForwardRefExoticComponent<any> | import("react").FunctionComponent<Omit<any, keyof ThemeProps<T>>>;
export { Badge, BottomSheet, Button, ButtonGroup, Card, Chip, Input, ListItem, LinearProgress, PricingCard, Tooltip, Switch, SocialIcon, Text, Divider, Dialog, CheckBox, SearchBar, Icon, colors, getIconType, registerCustomIconType, normalize, Tile, Slider, Avatar, Accessory, Rating, AirbnbRating, Header, Overlay, ThemeProvider, ThemeConsumer, ThemeContext, withBadge, withTheme, useTheme, makeStyles, Image, FAB, SpeedDial, Tab, TabView, };
export type { AvatarProps, ButtonProps, Colors, ChipProps, InputProps, IconProps, ListItemProps, SocialIconProps, OverlayProps, SearchBarProps, SearchBarAndroidProps, SearchBarDefaultProps, SearchBarIosProps, BadgeProps, CheckBoxProps, DividerProps, SliderProps, ButtonGroupProps, ImageProps, CardProps, DialogProps, DialogLoadingProps, DialogTitleProps, TileProps, AccessoryProps, HeaderProps, PricingCardProps, TooltipProps, BottomSheetProps, TapRatingProps, SwipeRatingProps, SwitchProps, TextProps, UpdateTheme, ReplaceTheme, FullTheme, Theme, LinearProgressProps, FABProps, SpeedDialActionProps, SpeedDialProps, ThemeProps, TabItemProps, TabProps, ListItemAccordionProps, ListItemSwipeableProps, TabViewProps, };
