import expo.modules.plugin.gradle.ExpoModuleExtension

plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

def getReactNativeDir = {
  return project.extensions.getByType(ExpoModuleExtension).reactNativeDir
}

def REACT_NATIVE_DIR = getReactNativeDir()

def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

group = 'host.exp.exponent'
version = '15.1.6'

android {
  if (rootProject.hasProperty("ndkPath")) {
    ndkPath rootProject.ext.ndkPath
  }
  if (rootProject.hasProperty("ndkVersion")) {
    ndkVersion rootProject.ext.ndkVersion
  }

  namespace "expo.modules.av"
  defaultConfig {
    versionCode 24
    versionName "15.1.6"

    externalNativeBuild {
      cmake {
        cppFlags "-O2 -frtti -fexceptions -Wall -fstack-protector-all"
        abiFilters (*reactNativeArchitectures())
        arguments "-DANDROID_STL=c++_shared",
            "-DREACT_NATIVE_DIR=${REACT_NATIVE_DIR}"
      }
    }
  }

  externalNativeBuild {
    cmake {
      path "CMakeLists.txt"
    }
  }

  buildFeatures {
    prefab true
  }

  packagingOptions {
    // Gradle will add cmake target dependencies into packaging.
    // Theses files are intermediated linking files to build reanimated and should not be in final package.
    excludes += [
        "**/libc++_shared.so",
        "**/libreactnative.so",
        "**/libreactnativejni.so",
        "**/libglog.so",
        "**/libjscexecutor.so",
        "**/libfbjni.so",
        "**/libfolly_json.so",
        "**/libhermes.so",
        "**/libjsi.so",
    ]
  }


  testOptions {
    unitTests.all {
      useJUnitPlatform()
    }
  }
}

dependencies {
  implementation 'com.facebook.react:react-android'

  compileOnly 'com.facebook.fbjni:fbjni:0.3.0'

  api 'com.google.android.exoplayer:exoplayer:2.18.1'
  api 'com.google.android.exoplayer:extension-okhttp:2.18.1'

  api "com.squareup.okhttp3:okhttp:3.14.9"
  api "com.squareup.okhttp3:okhttp-urlconnection:3.14.9"

  testImplementation 'org.junit.jupiter:junit-jupiter-api:5.5.1'
  testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:5.5.1"

  testImplementation 'io.mockk:mockk:1.12.3'
}
