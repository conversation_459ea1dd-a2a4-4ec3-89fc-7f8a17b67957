/**
 * This module was automatically generated by `ts-interface-builder`
 */
import * as t from "ts-interface-checker";
// tslint:disable:object-literal-key-quotes

export const IMediaPlayerVideoFrameObserver = t.iface([], {
  "onFrame": t.opt(t.func("void", t.param("frame", "VideoFrame"))),
});

const exportedTypeSuite: t.ITypeSuite = {
  IMediaPlayerVideoFrameObserver,
};
export default exportedTypeSuite;
