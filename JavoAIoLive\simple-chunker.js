// Simple Text Chunker - Parse text into chunks of 125 lines max
const fs = require('fs');

function chunkText(inputFile, maxLines = 125) {
    console.log(`📄 Reading file: ${inputFile}`);
    
    // Read the input file
    const text = fs.readFileSync(inputFile, 'utf8');
    const lines = text.split('\n');
    
    console.log(`📊 Total lines: ${lines.length}`);
    console.log(`🔢 Max lines per chunk: ${maxLines}`);
    
    // Create chunks
    const chunks = [];
    for (let i = 0; i < lines.length; i += maxLines) {
        const chunk = lines.slice(i, i + maxLines);
        const chunkInfo = {
            number: Math.floor(i / maxLines) + 1,
            startLine: i + 1,
            endLine: Math.min(i + maxLines, lines.length),
            totalLines: chunk.length,
            content: chunk.join('\n')
        };
        chunks.push(chunkInfo);
    }
    
    console.log(`📦 Created ${chunks.length} chunks`);
    
    // Save each chunk
    chunks.forEach(chunk => {
        const filename = `chunk_${String(chunk.number).padStart(3, '0')}.txt`;
        const header = `// Chunk ${chunk.number}
// Lines ${chunk.startLine}-${chunk.endLine} (${chunk.totalLines} lines)
// Generated: ${new Date().toISOString()}

`;
        const content = header + chunk.content;
        
        fs.writeFileSync(filename, content, 'utf8');
        console.log(`✅ Created: ${filename} (Lines ${chunk.startLine}-${chunk.endLine}, ${chunk.totalLines} lines)`);
    });
    
    // Create summary
    const summary = `# Text Chunking Summary
Generated: ${new Date().toISOString()}
Input file: ${inputFile}
Total lines: ${lines.length}
Max lines per chunk: ${maxLines}
Total chunks created: ${chunks.length}

## Chunk Details:
${chunks.map(c => `- chunk_${String(c.number).padStart(3, '0')}.txt: Lines ${c.startLine}-${c.endLine} (${c.totalLines} lines)`).join('\n')}
`;
    
    fs.writeFileSync('chunking_summary.md', summary, 'utf8');
    console.log('📋 Summary saved to: chunking_summary.md');
    
    console.log('\n🎉 Text chunking completed successfully!');
    return chunks;
}

// Get command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
    console.log('Usage: node simple-chunker.js <input-file> [max-lines]');
    console.log('Example: node simple-chunker.js large-file.txt 125');
    process.exit(1);
}

const inputFile = args[0];
const maxLines = parseInt(args[1]) || 125;

if (!fs.existsSync(inputFile)) {
    console.error(`❌ Error: Input file '${inputFile}' not found`);
    process.exit(1);
}

try {
    chunkText(inputFile, maxLines);
} catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
}
