{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAsB,MAAM,mBAAmB,CAAC;AAG7E,OAAO,aAAa,MAAM,qBAAqB,CAAC;AAEhD,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,cAAc,CAAC;AAErD,cAAc;AACd;;;GAGG;AACH,KAAK,UAAU,yBAAyB;IACtC,OAAO,aAAa,CAAC,yBAAyB,EAAE,CAAC;AACnD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,KAAK,UAAU,6BAA6B;IAC1C,OAAO,aAAa,CAAC,6BAA6B,EAAE,CAAC;AACvD,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,oBAAoB,CAAC;IACvD,SAAS,EAAE,yBAAyB;IACpC,aAAa,EAAE,6BAA6B;CAC7C,CAAC,CAAC;AAEH,cAAc;AACd;;;GAGG;AACH,KAAK,UAAU,6BAA6B;IAC1C,OAAO,aAAa,CAAC,6BAA6B,EAAE,CAAC;AACvD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,KAAK,UAAU,iCAAiC;IAC9C,OAAO,aAAa,CAAC,iCAAiC,EAAE,CAAC;AAC3D,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,oBAAoB,CAAC;IAC3D,SAAS,EAAE,6BAA6B;IACxC,aAAa,EAAE,iCAAiC;CACjD,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,GAAW,EACX,eAA8B,CAAC,IAAI,CAAC;IAEpC,OAAO,aAAa,CAAC,gBAAgB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AAC3D,CAAC;AAED,cAAc,gBAAgB,CAAC;AAC/B,cAAc,cAAc,CAAC;AAE7B;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG;IACpB,yBAAyB;IACzB,6BAA6B;IAC7B,6BAA6B;IAC7B,iCAAiC;IACjC,gBAAgB;CACjB,CAAC", "sourcesContent": ["import { createPermissionHook, PermissionResponse } from 'expo-modules-core';\n\nimport { BarcodeScanningResult, BarcodeType } from './Camera.types';\nimport CameraManager from './ExpoCameraManager';\n\nexport { default as CameraView } from './CameraView';\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing camera.\n * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n */\nasync function getCameraPermissionsAsync(): Promise<PermissionResponse> {\n  return CameraManager.getCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing camera.\n * On iOS this will require apps to specify an `NSCameraUsageDescription` entry in the **Info.plist**.\n * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n */\nasync function requestCameraPermissionsAsync(): Promise<PermissionResponse> {\n  return CameraManager.requestCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Check or request permissions to access the camera.\n * This uses both `requestCameraPermissionsAsync` and `getCameraPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = useCameraPermissions();\n * ```\n */\nexport const useCameraPermissions = createPermissionHook({\n  getMethod: getCameraPermissionsAsync,\n  requestMethod: requestCameraPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing microphone.\n * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n */\nasync function getMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n  return CameraManager.getMicrophonePermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing the microphone.\n * On iOS this will require apps to specify an `NSMicrophoneUsageDescription` entry in the **Info.plist**.\n * @return A promise that resolves to an object of type [PermissionResponse](#permissionresponse).\n */\nasync function requestMicrophonePermissionsAsync(): Promise<PermissionResponse> {\n  return CameraManager.requestMicrophonePermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Check or request permissions to access the microphone.\n * This uses both `requestMicrophonePermissionsAsync` and `getMicrophonePermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = Camera.useMicrophonePermissions();\n * ```\n */\nexport const useMicrophonePermissions = createPermissionHook({\n  getMethod: getMicrophonePermissionsAsync,\n  requestMethod: requestMicrophonePermissionsAsync,\n});\n\n/**\n * Scan bar codes from the image at the given URL.\n * @param url URL to get the image from.\n * @param barcodeTypes An array of bar code types. Defaults to all supported bar code types on\n * the platform.\n * > __Note:__ Only QR codes are supported on iOS.\n * On android, the barcode should take up the majority of the image for best results.\n * @return A possibly empty array of objects of the `BarcodeScanningResult` shape, where the type\n * refers to the barcode type that was scanned and the data is the information encoded in the barcode.\n */\nexport async function scanFromURLAsync(\n  url: string,\n  barcodeTypes: BarcodeType[] = ['qr']\n): Promise<BarcodeScanningResult[]> {\n  return CameraManager.scanFromURLAsync(url, barcodeTypes);\n}\n\nexport * from './Camera.types';\nexport * from './PictureRef';\n\n/**\n * @hidden\n */\nexport const Camera = {\n  getCameraPermissionsAsync,\n  requestCameraPermissionsAsync,\n  getMicrophonePermissionsAsync,\n  requestMicrophonePermissionsAsync,\n  scanFromURLAsync,\n};\n"]}