// Text Chunker Utility - Parse text into chunks of 125 lines max
// Usage: node text-chunker.js <input-file> [output-prefix]

const fs = require('fs');
const path = require('path');

function parseTextIntoChunks(inputText, maxLines = 125) {
    const lines = inputText.split('\n');
    const chunks = [];
    
    for (let i = 0; i < lines.length; i += maxLines) {
        const chunk = lines.slice(i, i + maxLines);
        chunks.push({
            chunkNumber: Math.floor(i / maxLines) + 1,
            startLine: i + 1,
            endLine: Math.min(i + maxLines, lines.length),
            totalLines: chunk.length,
            content: chunk.join('\n')
        });
    }
    
    return chunks;
}

function saveChunksToFiles(chunks, outputPrefix = 'chunk') {
    const results = [];
    
    chunks.forEach((chunk, index) => {
        const filename = `${outputPrefix}_${String(index + 1).padStart(3, '0')}.txt`;
        const header = `// Chunk ${chunk.chunkNumber}\n// Lines ${chunk.startLine}-${chunk.endLine} (${chunk.totalLines} lines)\n// Generated: ${new Date().toISOString()}\n\n`;
        const content = header + chunk.content;
        
        fs.writeFileSync(filename, content, 'utf8');
        results.push({
            filename,
            chunkNumber: chunk.chunkNumber,
            lineRange: `${chunk.startLine}-${chunk.endLine}`,
            totalLines: chunk.totalLines
        });
        
        console.log(`✅ Created: ${filename} (Lines ${chunk.startLine}-${chunk.endLine}, ${chunk.totalLines} lines)`);
    });
    
    return results;
}

function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node text-chunker.js <input-file> [output-prefix] [max-lines]');
        console.log('');
        console.log('Examples:');
        console.log('  node text-chunker.js large-file.txt');
        console.log('  node text-chunker.js log.txt log-chunk');
        console.log('  node text-chunker.js data.txt data 100');
        console.log('');
        console.log('Default max-lines: 125');
        process.exit(1);
    }
    
    const inputFile = args[0];
    const outputPrefix = args[1] || 'chunk';
    const maxLines = parseInt(args[2]) || 125;
    
    if (!fs.existsSync(inputFile)) {
        console.error(`❌ Error: Input file '${inputFile}' not found`);
        process.exit(1);
    }
    
    try {
        console.log(`📄 Reading file: ${inputFile}`);
        const inputText = fs.readFileSync(inputFile, 'utf8');
        const totalLines = inputText.split('\n').length;
        
        console.log(`📊 Total lines: ${totalLines}`);
        console.log(`🔢 Max lines per chunk: ${maxLines}`);
        console.log(`📦 Expected chunks: ${Math.ceil(totalLines / maxLines)}`);
        console.log('');
        
        const chunks = parseTextIntoChunks(inputText, maxLines);
        const results = saveChunksToFiles(chunks, outputPrefix);
        
        console.log('');
        console.log('🎉 Chunking completed successfully!');
        console.log(`📁 Created ${results.length} chunk files:`);
        
        results.forEach(result => {
            console.log(`   ${result.filename} - Lines ${result.lineRange} (${result.totalLines} lines)`);
        });
        
        // Create summary file
        const summaryContent = `# Text Chunking Summary
Generated: ${new Date().toISOString()}
Input file: ${inputFile}
Total lines: ${totalLines}
Max lines per chunk: ${maxLines}
Total chunks created: ${results.length}

## Chunk Details:
${results.map(r => `- ${r.filename}: Lines ${r.lineRange} (${r.totalLines} lines)`).join('\n')}
`;
        
        const summaryFile = `${outputPrefix}_summary.md`;
        fs.writeFileSync(summaryFile, summaryContent, 'utf8');
        console.log(`📋 Summary saved to: ${summaryFile}`);
        
    } catch (error) {
        console.error(`❌ Error processing file: ${error.message}`);
        process.exit(1);
    }
}

// Export functions for use as module
module.exports = {
    parseTextIntoChunks,
    saveChunksToFiles
};

// Run main function if called directly
if (require.main === module) {
    main();
}
