{"name": "react-native-size-matters", "version": "0.3.1", "description": "A React-Native utility belt for scaling the size your apps UI across different sized devices", "main": "index.js", "scripts": {"test": "jest --coverage", "test:watch": "jest --watch"}, "repository": {"type": "git", "url": "git+https://github.com/nirsky/react-native-size-matters.git"}, "keywords": ["javascript", "react-native", "scaling"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nirsky/react-native-size-matters/issues"}, "homepage": "https://github.com/nirsky/react-native-size-matters#readme", "peerDependencies": {"react-native": "*"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "babel-jest": "^24.6.0", "jest": "^24.6.1", "jest-cli": "^24.6.0"}}