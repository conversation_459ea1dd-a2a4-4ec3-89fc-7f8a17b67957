// App Configuration
export const APP_CONFIG = {
  name: 'JavoAIo Live Contractor Hub',
  version: '1.0.0',
  domain: 'javoaio.com',
};

// Firebase Configuration (placeholder - will be replaced with actual config)
export const FIREBASE_CONFIG = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY || '',
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || '',
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID || '',
};

// Agora Configuration
export const AGORA_CONFIG = {
  appId: process.env.EXPO_PUBLIC_AGORA_APP_ID || '78ca499e687e40f693972e1656abd178',
  // Note: App Certificate should NEVER be in client code - only used in backend
};

// Mux Configuration (placeholder - will be replaced with actual config)
export const MUX_CONFIG = {
  // Note: Mux credentials should NEVER be in client code - only used in backend
  playbackId: process.env.EXPO_PUBLIC_MUX_PLAYBACK_ID || '',
};

// API Endpoints
export const API_ENDPOINTS = {
  generateAgoraToken: '/api/agora/token',
  createStream: '/api/streams/create',
  endStream: '/api/streams/end',
  getActiveStreams: '/api/streams/active',
};

// Stream Settings
export const STREAM_SETTINGS = {
  maxTitleLength: 100,
  maxDescriptionLength: 500,
  maxChatMessageLength: 200,
  defaultChannelPrefix: 'javoaio_',
};

// UI Constants
export const COLORS = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  background: '#F5F5F5',
  surface: '#FFFFFF',
  text: '#333333',
  textSecondary: '#666666',
  border: '#E0E0E0',
  live: '#FF4444',
};

export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
};
