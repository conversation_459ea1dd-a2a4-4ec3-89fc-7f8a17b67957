import { SpatialAudioParams } from '../AgoraBase';
import { AudioDualMonoMode, IAudioPcmFrameSink, IAudioSpectrumObserver, RawAudioFrameOpModeType, RenderModeType } from '../AgoraMediaBase';
import { MediaPlayerState, MediaSource, PlayerStreamInfo } from '../AgoraMediaPlayerTypes';
import { IMediaPlayer, IMediaPlayerCacheManager, IMediaPlayerVideoFrameObserver } from '../IAgoraMediaPlayer';
import { IMediaPlayerSourceObserver } from '../IAgoraMediaPlayerSource';
export declare class IMediaPlayerImpl implements IMediaPlayer {
    getMediaPlayerId(): number;
    protected getApiTypeFromGetMediaPlayerId(): string;
    open(url: string, startPos: number): number;
    protected getApiTypeFromOpen(url: string, startPos: number): string;
    openWithMediaSource(source: MediaSource): number;
    protected getApiTypeFromOpenWithMediaSource(source: MediaSource): string;
    play(): number;
    protected getApiTypeFromPlay(): string;
    pause(): number;
    protected getApiTypeFromPause(): string;
    stop(): number;
    protected getApiTypeFromStop(): string;
    resume(): number;
    protected getApiTypeFromResume(): string;
    seek(newPos: number): number;
    protected getApiTypeFromSeek(newPos: number): string;
    setAudioPitch(pitch: number): number;
    protected getApiTypeFromSetAudioPitch(pitch: number): string;
    getDuration(): number;
    protected getApiTypeFromGetDuration(): string;
    getPlayPosition(): number;
    protected getApiTypeFromGetPlayPosition(): string;
    getStreamCount(): number;
    protected getApiTypeFromGetStreamCount(): string;
    getStreamInfo(index: number): PlayerStreamInfo;
    protected getApiTypeFromGetStreamInfo(index: number): string;
    setLoopCount(loopCount: number): number;
    protected getApiTypeFromSetLoopCount(loopCount: number): string;
    setPlaybackSpeed(speed: number): number;
    protected getApiTypeFromSetPlaybackSpeed(speed: number): string;
    selectAudioTrack(index: number): number;
    protected getApiTypeFromSelectAudioTrack(index: number): string;
    selectMultiAudioTrack(playoutTrackIndex: number, publishTrackIndex: number): number;
    protected getApiTypeFromSelectMultiAudioTrack(playoutTrackIndex: number, publishTrackIndex: number): string;
    takeScreenshot(filename: string): number;
    protected getApiTypeFromTakeScreenshot(filename: string): string;
    selectInternalSubtitle(index: number): number;
    protected getApiTypeFromSelectInternalSubtitle(index: number): string;
    setExternalSubtitle(url: string): number;
    protected getApiTypeFromSetExternalSubtitle(url: string): string;
    getState(): MediaPlayerState;
    protected getApiTypeFromGetState(): string;
    mute(muted: boolean): number;
    protected getApiTypeFromMute(muted: boolean): string;
    getMute(): boolean;
    protected getApiTypeFromGetMute(): string;
    adjustPlayoutVolume(volume: number): number;
    protected getApiTypeFromAdjustPlayoutVolume(volume: number): string;
    getPlayoutVolume(): number;
    protected getApiTypeFromGetPlayoutVolume(): string;
    adjustPublishSignalVolume(volume: number): number;
    protected getApiTypeFromAdjustPublishSignalVolume(volume: number): string;
    getPublishSignalVolume(): number;
    protected getApiTypeFromGetPublishSignalVolume(): string;
    setView(view: any): number;
    protected getApiTypeFromSetView(view: any): string;
    setRenderMode(renderMode: RenderModeType): number;
    protected getApiTypeFromSetRenderMode(renderMode: RenderModeType): string;
    registerPlayerSourceObserver(observer: IMediaPlayerSourceObserver): number;
    protected getApiTypeFromRegisterPlayerSourceObserver(observer: IMediaPlayerSourceObserver): string;
    unregisterPlayerSourceObserver(observer: IMediaPlayerSourceObserver): number;
    protected getApiTypeFromUnregisterPlayerSourceObserver(observer: IMediaPlayerSourceObserver): string;
    registerAudioFrameObserver(observer: IAudioPcmFrameSink, mode?: RawAudioFrameOpModeType): number;
    protected getApiTypeFromRegisterAudioFrameObserver(observer: IAudioPcmFrameSink, mode?: RawAudioFrameOpModeType): string;
    unregisterAudioFrameObserver(observer: IAudioPcmFrameSink): number;
    protected getApiTypeFromUnregisterAudioFrameObserver(observer: IAudioPcmFrameSink): string;
    registerVideoFrameObserver(observer: IMediaPlayerVideoFrameObserver): number;
    protected getApiTypeFromRegisterVideoFrameObserver(observer: IMediaPlayerVideoFrameObserver): string;
    unregisterVideoFrameObserver(observer: IMediaPlayerVideoFrameObserver): number;
    protected getApiTypeFromUnregisterVideoFrameObserver(observer: IMediaPlayerVideoFrameObserver): string;
    registerMediaPlayerAudioSpectrumObserver(observer: IAudioSpectrumObserver, intervalInMS: number): number;
    protected getApiTypeFromRegisterMediaPlayerAudioSpectrumObserver(observer: IAudioSpectrumObserver, intervalInMS: number): string;
    unregisterMediaPlayerAudioSpectrumObserver(observer: IAudioSpectrumObserver): number;
    protected getApiTypeFromUnregisterMediaPlayerAudioSpectrumObserver(observer: IAudioSpectrumObserver): string;
    setAudioDualMonoMode(mode: AudioDualMonoMode): number;
    protected getApiTypeFromSetAudioDualMonoMode(mode: AudioDualMonoMode): string;
    getPlayerSdkVersion(): string;
    protected getApiTypeFromGetPlayerSdkVersion(): string;
    getPlaySrc(): string;
    protected getApiTypeFromGetPlaySrc(): string;
    openWithAgoraCDNSrc(src: string, startPos: number): number;
    protected getApiTypeFromOpenWithAgoraCDNSrc(src: string, startPos: number): string;
    getAgoraCDNLineCount(): number;
    protected getApiTypeFromGetAgoraCDNLineCount(): string;
    switchAgoraCDNLineByIndex(index: number): number;
    protected getApiTypeFromSwitchAgoraCDNLineByIndex(index: number): string;
    getCurrentAgoraCDNIndex(): number;
    protected getApiTypeFromGetCurrentAgoraCDNIndex(): string;
    enableAutoSwitchAgoraCDN(enable: boolean): number;
    protected getApiTypeFromEnableAutoSwitchAgoraCDN(enable: boolean): string;
    renewAgoraCDNSrcToken(token: string, ts: number): number;
    protected getApiTypeFromRenewAgoraCDNSrcToken(token: string, ts: number): string;
    switchAgoraCDNSrc(src: string, syncPts?: boolean): number;
    protected getApiTypeFromSwitchAgoraCDNSrc(src: string, syncPts?: boolean): string;
    switchSrc(src: string, syncPts?: boolean): number;
    protected getApiTypeFromSwitchSrc(src: string, syncPts?: boolean): string;
    preloadSrc(src: string, startPos: number): number;
    protected getApiTypeFromPreloadSrc(src: string, startPos: number): string;
    playPreloadedSrc(src: string): number;
    protected getApiTypeFromPlayPreloadedSrc(src: string): string;
    unloadSrc(src: string): number;
    protected getApiTypeFromUnloadSrc(src: string): string;
    setSpatialAudioParams(params: SpatialAudioParams): number;
    protected getApiTypeFromSetSpatialAudioParams(params: SpatialAudioParams): string;
    setSoundPositionParams(pan: number, gain: number): number;
    protected getApiTypeFromSetSoundPositionParams(pan: number, gain: number): string;
    setPlayerOptionInInt(key: string, value: number): number;
    protected getApiTypeFromSetPlayerOptionInInt(key: string, value: number): string;
    setPlayerOptionInString(key: string, value: string): number;
    protected getApiTypeFromSetPlayerOptionInString(key: string, value: string): string;
}
export declare class IMediaPlayerCacheManagerImpl implements IMediaPlayerCacheManager {
    removeAllCaches(): number;
    protected getApiTypeFromRemoveAllCaches(): string;
    removeOldCache(): number;
    protected getApiTypeFromRemoveOldCache(): string;
    removeCacheByUri(uri: string): number;
    protected getApiTypeFromRemoveCacheByUri(uri: string): string;
    setCacheDir(path: string): number;
    protected getApiTypeFromSetCacheDir(path: string): string;
    setMaxCacheFileCount(count: number): number;
    protected getApiTypeFromSetMaxCacheFileCount(count: number): string;
    setMaxCacheFileSize(cacheSize: number): number;
    protected getApiTypeFromSetMaxCacheFileSize(cacheSize: number): string;
    enableAutoRemoveCache(enable: boolean): number;
    protected getApiTypeFromEnableAutoRemoveCache(enable: boolean): string;
    getCacheDir(length: number): string;
    protected getApiTypeFromGetCacheDir(length: number): string;
    getMaxCacheFileCount(): number;
    protected getApiTypeFromGetMaxCacheFileCount(): string;
    getMaxCacheFileSize(): number;
    protected getApiTypeFromGetMaxCacheFileSize(): string;
    getCacheFileCount(): number;
    protected getApiTypeFromGetCacheFileCount(): string;
}
export declare function processIMediaPlayerVideoFrameObserver(handler: IMediaPlayerVideoFrameObserver, event: string, jsonParams: any): void;
//# sourceMappingURL=IAgoraMediaPlayerImpl.d.ts.map