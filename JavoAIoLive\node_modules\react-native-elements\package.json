{"name": "react-native-elements", "version": "3.4.3", "description": "React Native Elements & UI Toolkit", "main": "dist/index.js", "types": "dist/index.d.ts", "prepublish": "tsc", "files": ["dist"], "keywords": ["react-native", "reactjs", "reactnative", "bootstrap"], "scripts": {"build": "tsc", "test": "jest", "test:update": "jest -u", "test:ci": "jest --runInBand", "test:watch": "jest --watch", "postinstall": "opencollective-postinstall || exit 0", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "prettify": "prettier --single-quote --trailing-comma=es5 --write './**/*.md'", "clean-install": "rimraf node_modules && yarn", "changelog": "auto-changelog -p", "docs": "cd website && yarn start"}, "author": "Nader Dabit & Monte Thakkar", "license": "MIT", "bugs": {"url": "https://github.com/react-native-elements/react-native-elements/issues"}, "homepage": "https://reactnativeelements.com/", "collective": {"type": "opencollective", "url": "https://opencollective.com/react-native-elements", "logo": "https://opencollective.com/react-native-elements/logo.txt"}, "dependencies": {"@types/react-native-vector-icons": "^6.4.6", "color": "^3.1.2", "deepmerge": "^4.2.2", "hoist-non-react-statics": "^3.3.2", "lodash.isequal": "^4.5.0", "opencollective-postinstall": "^2.0.3", "react-native-ratings": "8.0.4", "react-native-size-matters": "^0.3.1"}, "devDependencies": {"@react-native-community/eslint-config": "^2.0.0", "@testing-library/react-native": "^7.0.2", "@types/color": "^3.0.1", "@types/enzyme": "^3.10.8", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^26.0.23", "@types/lodash.isequal": "^4.5.5", "@types/react-native": "*", "@types/react-test-renderer": "^17.0.0", "auto-changelog": "^2.2.1", "babel-jest": "^26.3.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.4", "enzyme-to-json": "^3.5.0", "eslint": "^7.9.0", "husky": "^4.3.0", "jest": "^26.4.2", "jest-transform-stub": "^2.0.0", "lint-staged": "^10.4.0", "metro-react-native-babel-preset": "^0.63.0", "react": "16.13.1", "react-dom": "16.13.1", "react-native": "0.63.2", "react-native-safe-area-context": "^3.1.9", "react-native-vector-icons": "^7.0.0", "react-test-renderer": "^16.13.1", "rimraf": "^3.0.2", "typescript": "^4.1.3", "utility-types": "^3.10.0"}, "peerDependencies": {"react-native-safe-area-context": ">= 3.0.0", "react-native-vector-icons": ">7.0.0"}, "jest": {"preset": "react-native", "timers": "fake", "coverageDirectory": "./coverage/", "testPathIgnorePatterns": ["./src/searchbar/__tests__/common.tsx", "<rootDir>/node_modules", "<rootDir>/dist"], "coveragePathIgnorePatterns": ["./src/searchbar/__tests__/common.tsx"], "collectCoverageFrom": ["src/**/*.tsx", "!src/index.tsx", "!src/helpers/*.tsx"], "collectCoverage": true, "globals": {"__DEV__": true}, "setupFilesAfterEnv": ["<rootDir>/.ci/setupTests.ts"], "testRegex": "/__tests__/.*\\.(ts|tsx|js)$", "transform": {".+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2)$": "jest-transform-stub"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "bash -c tsc", "jest --bail --findRelatedTests"], "src/**/*.{js,jsx}": ["eslint --fix", "jest --bail --findRelatedTests"], "**/*.md": ["prettier --single-quote --trailing-comma=es5 --write"]}, "directories": {"doc": "docs"}, "repository": {"type": "git", "url": "git+https://github.com/react-native-elements/react-native-elements.git"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}