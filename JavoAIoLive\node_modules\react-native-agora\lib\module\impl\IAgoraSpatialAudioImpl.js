// @ts-ignore
export class ILocalSpatialAudioEngineImpl {
  release() {
    const apiType = this.getApiTypeFromRelease();
    const jsonParams = {};
    callIrisApi.call(this, apiType, jsonParams);
  }
  getApiTypeFromRelease() {
    return 'LocalSpatialAudioEngine_release';
  }
  initialize() {
    const apiType = this.getApiTypeFromInitialize();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromInitialize() {
    return 'LocalSpatialAudioEngine_initialize_cf94fbf';
  }
  updateRemotePosition(uid, posInfo) {
    const apiType = this.getApiTypeFromUpdateRemotePosition(uid, posInfo);
    const jsonParams = {
      uid: uid,
      posInfo: posInfo,
      toJSON: () => {
        return {
          uid: uid,
          posInfo: posInfo
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromUpdateRemotePosition(uid, posInfo) {
    return 'LocalSpatialAudioEngine_updateRemotePosition_adc0909';
  }
  updateRemotePositionEx(uid, posInfo, connection) {
    const apiType = this.getApiTypeFromUpdateRemotePositionEx(uid, posInfo, connection);
    const jsonParams = {
      uid: uid,
      posInfo: posInfo,
      connection: connection,
      toJSON: () => {
        return {
          uid: uid,
          posInfo: posInfo,
          connection: connection
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromUpdateRemotePositionEx(uid, posInfo, connection) {
    return 'LocalSpatialAudioEngine_updateRemotePositionEx_f0252d9';
  }
  removeRemotePosition(uid) {
    const apiType = this.getApiTypeFromRemoveRemotePosition(uid);
    const jsonParams = {
      uid: uid,
      toJSON: () => {
        return {
          uid: uid
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromRemoveRemotePosition(uid) {
    return 'LocalSpatialAudioEngine_removeRemotePosition_c8d091a';
  }
  removeRemotePositionEx(uid, connection) {
    const apiType = this.getApiTypeFromRemoveRemotePositionEx(uid, connection);
    const jsonParams = {
      uid: uid,
      connection: connection,
      toJSON: () => {
        return {
          uid: uid,
          connection: connection
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromRemoveRemotePositionEx(uid, connection) {
    return 'LocalSpatialAudioEngine_removeRemotePositionEx_58a9850';
  }
  clearRemotePositionsEx(connection) {
    const apiType = this.getApiTypeFromClearRemotePositionsEx(connection);
    const jsonParams = {
      connection: connection,
      toJSON: () => {
        return {
          connection: connection
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromClearRemotePositionsEx(connection) {
    return 'LocalSpatialAudioEngine_clearRemotePositionsEx_c81e1a4';
  }
  updateSelfPositionEx(position, axisForward, axisRight, axisUp, connection) {
    const apiType = this.getApiTypeFromUpdateSelfPositionEx(position, axisForward, axisRight, axisUp, connection);
    const jsonParams = {
      position: position,
      axisForward: axisForward,
      axisRight: axisRight,
      axisUp: axisUp,
      connection: connection,
      toJSON: () => {
        return {
          position: position,
          axisForward: axisForward,
          axisRight: axisRight,
          axisUp: axisUp,
          connection: connection
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromUpdateSelfPositionEx(position, axisForward, axisRight, axisUp, connection) {
    return 'LocalSpatialAudioEngine_updateSelfPositionEx_502183a';
  }
  setMaxAudioRecvCount(maxCount) {
    const apiType = this.getApiTypeFromSetMaxAudioRecvCount(maxCount);
    const jsonParams = {
      maxCount: maxCount,
      toJSON: () => {
        return {
          maxCount: maxCount
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetMaxAudioRecvCount(maxCount) {
    return 'LocalSpatialAudioEngine_setMaxAudioRecvCount_46f8ab7';
  }
  setAudioRecvRange(range) {
    const apiType = this.getApiTypeFromSetAudioRecvRange(range);
    const jsonParams = {
      range: range,
      toJSON: () => {
        return {
          range: range
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetAudioRecvRange(range) {
    return 'LocalSpatialAudioEngine_setAudioRecvRange_685e803';
  }
  setDistanceUnit(unit) {
    const apiType = this.getApiTypeFromSetDistanceUnit(unit);
    const jsonParams = {
      unit: unit,
      toJSON: () => {
        return {
          unit: unit
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetDistanceUnit(unit) {
    return 'LocalSpatialAudioEngine_setDistanceUnit_685e803';
  }
  updateSelfPosition(position, axisForward, axisRight, axisUp) {
    const apiType = this.getApiTypeFromUpdateSelfPosition(position, axisForward, axisRight, axisUp);
    const jsonParams = {
      position: position,
      axisForward: axisForward,
      axisRight: axisRight,
      axisUp: axisUp,
      toJSON: () => {
        return {
          position: position,
          axisForward: axisForward,
          axisRight: axisRight,
          axisUp: axisUp
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromUpdateSelfPosition(position, axisForward, axisRight, axisUp) {
    return 'LocalSpatialAudioEngine_updateSelfPosition_9c9930f';
  }
  updatePlayerPositionInfo(playerId, positionInfo) {
    const apiType = this.getApiTypeFromUpdatePlayerPositionInfo(playerId, positionInfo);
    const jsonParams = {
      playerId: playerId,
      positionInfo: positionInfo,
      toJSON: () => {
        return {
          playerId: playerId,
          positionInfo: positionInfo
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromUpdatePlayerPositionInfo(playerId, positionInfo) {
    return 'LocalSpatialAudioEngine_updatePlayerPositionInfo_b37c59d';
  }
  setParameters(params) {
    const apiType = this.getApiTypeFromSetParameters(params);
    const jsonParams = {
      params: params,
      toJSON: () => {
        return {
          params: params
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetParameters(params) {
    return 'LocalSpatialAudioEngine_setParameters_3a2037f';
  }
  muteLocalAudioStream(mute) {
    const apiType = this.getApiTypeFromMuteLocalAudioStream(mute);
    const jsonParams = {
      mute: mute,
      toJSON: () => {
        return {
          mute: mute
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromMuteLocalAudioStream(mute) {
    return 'LocalSpatialAudioEngine_muteLocalAudioStream_5039d15';
  }
  muteAllRemoteAudioStreams(mute) {
    const apiType = this.getApiTypeFromMuteAllRemoteAudioStreams(mute);
    const jsonParams = {
      mute: mute,
      toJSON: () => {
        return {
          mute: mute
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromMuteAllRemoteAudioStreams(mute) {
    return 'LocalSpatialAudioEngine_muteAllRemoteAudioStreams_5039d15';
  }
  muteRemoteAudioStream(uid, mute) {
    const apiType = this.getApiTypeFromMuteRemoteAudioStream(uid, mute);
    const jsonParams = {
      uid: uid,
      mute: mute,
      toJSON: () => {
        return {
          uid: uid,
          mute: mute
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromMuteRemoteAudioStream(uid, mute) {
    return 'LocalSpatialAudioEngine_muteRemoteAudioStream_dbdc15a';
  }
  setRemoteAudioAttenuation(uid, attenuation, forceSet) {
    const apiType = this.getApiTypeFromSetRemoteAudioAttenuation(uid, attenuation, forceSet);
    const jsonParams = {
      uid: uid,
      attenuation: attenuation,
      forceSet: forceSet,
      toJSON: () => {
        return {
          uid: uid,
          attenuation: attenuation,
          forceSet: forceSet
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetRemoteAudioAttenuation(uid, attenuation, forceSet) {
    return 'LocalSpatialAudioEngine_setRemoteAudioAttenuation_74c3e98';
  }
  setZones(zones, zoneCount) {
    const apiType = this.getApiTypeFromSetZones(zones, zoneCount);
    const jsonParams = {
      zones: zones,
      zoneCount: zoneCount,
      toJSON: () => {
        return {
          zones: zones,
          zoneCount: zoneCount
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetZones(zones, zoneCount) {
    return 'LocalSpatialAudioEngine_setZones_414a27e';
  }
  setPlayerAttenuation(playerId, attenuation, forceSet) {
    const apiType = this.getApiTypeFromSetPlayerAttenuation(playerId, attenuation, forceSet);
    const jsonParams = {
      playerId: playerId,
      attenuation: attenuation,
      forceSet: forceSet,
      toJSON: () => {
        return {
          playerId: playerId,
          attenuation: attenuation,
          forceSet: forceSet
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetPlayerAttenuation(playerId, attenuation, forceSet) {
    return 'LocalSpatialAudioEngine_setPlayerAttenuation_a15bc51';
  }
  clearRemotePositions() {
    const apiType = this.getApiTypeFromClearRemotePositions();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromClearRemotePositions() {
    return 'LocalSpatialAudioEngine_clearRemotePositions';
  }
}
import { callIrisApi } from '../internal/IrisApiEngine';
//# sourceMappingURL=IAgoraSpatialAudioImpl.js.map