import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Image,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList, LiveStream } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const [streams, setStreams] = useState<LiveStream[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data for now - will be replaced with Firebase data
  const mockStreams: LiveStream[] = [
    {
      id: '1',
      title: 'Kitchen Renovation Tips',
      description: 'Learn how to renovate your kitchen on a budget',
      broadcasterUid: 'user1',
      broadcasterName: 'John Builder',
      channelName: 'kitchen_reno_123',
      isActive: true,
      viewerCount: 45,
      startTime: new Date(),
    },
    {
      id: '2',
      title: 'Bathroom Tile Installation',
      description: 'Step-by-step guide to installing bathroom tiles',
      broadcasterUid: 'user2',
      broadcasterName: 'Sarah Contractor',
      channelName: 'tile_install_456',
      isActive: true,
      viewerCount: 23,
      startTime: new Date(),
    },
  ];

  useEffect(() => {
    loadStreams();
  }, []);

  const loadStreams = async () => {
    try {
      // TODO: Load from Firebase
      setStreams(mockStreams);
    } catch (error) {
      console.error('Error loading streams:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStreams();
    setRefreshing(false);
  };

  const joinStream = (stream: LiveStream) => {
    navigation.navigate('ViewStream', { streamId: stream.id });
  };

  const renderStreamItem = ({ item }: { item: LiveStream }) => (
    <TouchableOpacity style={styles.streamCard} onPress={() => joinStream(item)}>
      <View style={styles.streamThumbnail}>
        <View style={styles.liveBadge}>
          <Text style={styles.liveText}>LIVE</Text>
        </View>
        <View style={styles.viewerCount}>
          <Text style={styles.viewerText}>{item.viewerCount} viewers</Text>
        </View>
      </View>
      
      <View style={styles.streamInfo}>
        <Text style={styles.streamTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={styles.broadcasterName}>{item.broadcasterName}</Text>
        <Text style={styles.streamDescription} numberOfLines={2}>
          {item.description}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Live Streams</Text>
        <Text style={styles.headerSubtitle}>Home Improvement & Construction</Text>
      </View>

      <FlatList
        data={streams}
        renderItem={renderStreamItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.streamsList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No live streams at the moment</Text>
            <Text style={styles.emptySubtext}>Check back later or start your own stream!</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  streamsList: {
    padding: 15,
  },
  streamCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  streamThumbnail: {
    height: 200,
    backgroundColor: '#ddd',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  liveBadge: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: '#ff4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewerCount: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  viewerText: {
    color: '#fff',
    fontSize: 12,
  },
  streamInfo: {
    padding: 15,
  },
  streamTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  broadcasterName: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 5,
  },
  streamDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
  },
});

export default HomeScreen;
