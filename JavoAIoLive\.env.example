# Agora Configuration
# Get your App ID from: https://console.agora.io/
EXPO_PUBLIC_AGORA_APP_ID=your_agora_app_id_here

# Agora App Certificate (optional, for token generation)
AGORA_APP_CERTIFICATE=your_app_certificate_here

# Environment
NODE_ENV=development

# API Configuration
API_BASE_URL=http://localhost:3000

# Firebase Configuration (if using)
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id

# Other Configuration
DEBUG_MODE=true
LOG_LEVEL=debug
