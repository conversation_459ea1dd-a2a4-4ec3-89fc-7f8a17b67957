{"version": 3, "file": "WebCapabilityUtils.js", "sourceRoot": "", "sources": ["../../src/web/WebCapabilityUtils.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,UAAU,4BAA4B,CAAC,KAAa;IACxD,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,OAAO;YACV,OAAO,IAAI,CAAC;QACd,KAAK,IAAI,CAAC;QACV,KAAK,KAAK,CAAC;QACX,KAAK,MAAM,CAAC;QACZ;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,+BAA+B,CAAC,KAAa;IAC3D,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,IAAI,CAAC;QACV,KAAK,MAAM;YACT,OAAO,YAAY,CAAC;QACtB,KAAK,KAAK;YACR,OAAO,MAAM,CAAC;QAChB,KAAK,YAAY;YACf,OAAO,aAAa,CAAC;QACvB;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,4BAA4B,CAAC,KAAa;IACxD,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,IAAI,CAAC;QACV,KAAK,MAAM;YACT,OAAO,YAAY,CAAC;QACtB,KAAK,KAAK;YACR,OAAO,QAAQ,CAAC;QAClB,KAAK,YAAY;YACf,OAAO,aAAa,CAAC;QACvB;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC", "sourcesContent": ["/*\n * Native web camera (Android) has a torch: boolean\n */\nexport function convertFlashModeJSONToNative(input: string): boolean {\n  switch (input) {\n    case 'torch':\n      return true;\n    case 'on':\n    case 'off':\n    case 'auto':\n    default:\n      return false;\n  }\n}\n\nexport function convertWhiteBalanceJSONToNative(input: string): MeteringMode | undefined {\n  switch (input) {\n    case 'on':\n    case 'auto':\n      return 'continuous';\n    case 'off':\n      return 'none';\n    case 'singleShot':\n      return 'single-shot';\n    default:\n      return undefined;\n  }\n}\n\nexport function convertAutoFocusJSONToNative(input: string): MeteringMode | undefined {\n  switch (input) {\n    case 'on':\n    case 'auto':\n      return 'continuous';\n    case 'off':\n      return 'manual';\n    case 'singleShot':\n      return 'single-shot';\n    default:\n      return undefined;\n  }\n}\n"]}