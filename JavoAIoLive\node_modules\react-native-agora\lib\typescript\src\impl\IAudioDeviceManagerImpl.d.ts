import { AudioDeviceInfo } from '../IAgoraRtcEngine';
import { IAudioDeviceManager } from '../IAudioDeviceManager';
export declare class IAudioDeviceManagerImpl implements IAudioDeviceManager {
    enumeratePlaybackDevices(): AudioDeviceInfo[];
    protected getApiTypeFromEnumeratePlaybackDevices(): string;
    enumerateRecordingDevices(): AudioDeviceInfo[];
    protected getApiTypeFromEnumerateRecordingDevices(): string;
    setPlaybackDevice(deviceId: string): number;
    protected getApiTypeFromSetPlaybackDevice(deviceId: string): string;
    getPlaybackDevice(): string;
    protected getApiTypeFromGetPlaybackDevice(): string;
    getPlaybackDeviceInfo(): AudioDeviceInfo;
    protected getApiTypeFromGetPlaybackDeviceInfo(): string;
    setPlaybackDeviceVolume(volume: number): number;
    protected getApiTypeFromSetPlaybackDeviceVolume(volume: number): string;
    getPlaybackDeviceVolume(): number;
    protected getApiTypeFromGetPlaybackDeviceVolume(): string;
    setRecordingDevice(deviceId: string): number;
    protected getApiTypeFromSetRecordingDevice(deviceId: string): string;
    getRecordingDevice(): string;
    protected getApiTypeFromGetRecordingDevice(): string;
    getRecordingDeviceInfo(): AudioDeviceInfo;
    protected getApiTypeFromGetRecordingDeviceInfo(): string;
    setRecordingDeviceVolume(volume: number): number;
    protected getApiTypeFromSetRecordingDeviceVolume(volume: number): string;
    getRecordingDeviceVolume(): number;
    protected getApiTypeFromGetRecordingDeviceVolume(): string;
    setLoopbackDevice(deviceId: string): number;
    protected getApiTypeFromSetLoopbackDevice(deviceId: string): string;
    getLoopbackDevice(): string;
    protected getApiTypeFromGetLoopbackDevice(): string;
    setPlaybackDeviceMute(mute: boolean): number;
    protected getApiTypeFromSetPlaybackDeviceMute(mute: boolean): string;
    getPlaybackDeviceMute(): boolean;
    protected getApiTypeFromGetPlaybackDeviceMute(): string;
    setRecordingDeviceMute(mute: boolean): number;
    protected getApiTypeFromSetRecordingDeviceMute(mute: boolean): string;
    getRecordingDeviceMute(): boolean;
    protected getApiTypeFromGetRecordingDeviceMute(): string;
    startPlaybackDeviceTest(testAudioFilePath: string): number;
    protected getApiTypeFromStartPlaybackDeviceTest(testAudioFilePath: string): string;
    stopPlaybackDeviceTest(): number;
    protected getApiTypeFromStopPlaybackDeviceTest(): string;
    startRecordingDeviceTest(indicationInterval: number): number;
    protected getApiTypeFromStartRecordingDeviceTest(indicationInterval: number): string;
    stopRecordingDeviceTest(): number;
    protected getApiTypeFromStopRecordingDeviceTest(): string;
    startAudioDeviceLoopbackTest(indicationInterval: number): number;
    protected getApiTypeFromStartAudioDeviceLoopbackTest(indicationInterval: number): string;
    stopAudioDeviceLoopbackTest(): number;
    protected getApiTypeFromStopAudioDeviceLoopbackTest(): string;
    followSystemPlaybackDevice(enable: boolean): number;
    protected getApiTypeFromFollowSystemPlaybackDevice(enable: boolean): string;
    followSystemRecordingDevice(enable: boolean): number;
    protected getApiTypeFromFollowSystemRecordingDevice(enable: boolean): string;
    followSystemLoopbackDevice(enable: boolean): number;
    protected getApiTypeFromFollowSystemLoopbackDevice(enable: boolean): string;
    release(): void;
    protected getApiTypeFromRelease(): string;
    getPlaybackDefaultDevice(): AudioDeviceInfo;
    protected getApiTypeFromGetPlaybackDefaultDevice(): string;
    getRecordingDefaultDevice(): AudioDeviceInfo;
    protected getApiTypeFromGetRecordingDefaultDevice(): string;
}
//# sourceMappingURL=IAudioDeviceManagerImpl.d.ts.map