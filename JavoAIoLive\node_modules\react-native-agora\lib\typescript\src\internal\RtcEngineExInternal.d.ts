import { AudioEncodedFrameObserverConfig, ClientRoleOptions, ClientRoleType, IAudioEncodedFrameObserver, RecorderStreamInfo, SimulcastStreamConfig, SimulcastStreamMode } from '../AgoraBase';
import { IAudioSpectrumObserver } from '../AgoraMediaBase';
import { IH265Transcoder } from '../IAgoraH265Transcoder';
import { IMediaEngine } from '../IAgoraMediaEngine';
import { IMediaPlayer } from '../IAgoraMediaPlayer';
import { IMediaRecorder } from '../IAgoraMediaRecorder';
import { IMusicContentCenter } from '../IAgoraMusicContentCenter';
import { ChannelMediaOptions, DirectCdnStreamingMediaOptions, IDirectCdnStreamingEventHandler, IMetadataObserver, IRtcEngineEventHandler, IVideoDeviceManager, LeaveChannelOptions, MetadataType, RtcEngineContext, SDKBuildInfo } from '../IAgoraRtcEngine';
import { RtcConnection } from '../IAgoraRtcEngineEx';
import { ILocalSpatialAudioEngine } from '../IAgoraSpatialAudio';
import { IAudioDeviceManager } from '../IAudioDeviceManager';
import { IRtcEngineEvent } from '../extension/IAgoraRtcEngineExtension';
import { IRtcEngineExImpl } from '../impl/IAgoraRtcEngineExImpl';
export declare class RtcEngineExInternal extends IRtcEngineExImpl {
    static _event_handlers: IRtcEngineEventHandler[];
    static _direct_cdn_streaming_event_handler: IDirectCdnStreamingEventHandler[];
    static _metadata_observer: IMetadataObserver[];
    static _audio_encoded_frame_observers: IAudioEncodedFrameObserver[];
    static _audio_spectrum_observers: IAudioSpectrumObserver[];
    private _media_engine;
    private _music_content_center;
    private _local_spatial_audio_engine;
    private _h265_transcoder;
    initialize(context: RtcEngineContext): number;
    release(sync?: boolean): void;
    _addListenerPreCheck<EventType extends keyof IRtcEngineEvent>(eventType: EventType): boolean;
    addListener<EventType extends keyof IRtcEngineEvent>(eventType: EventType, listener: IRtcEngineEvent[EventType]): void;
    removeListener<EventType extends keyof IRtcEngineEvent>(eventType: EventType, listener?: IRtcEngineEvent[EventType]): void;
    removeAllListeners<EventType extends keyof IRtcEngineEvent>(eventType?: EventType): void;
    getVersion(): SDKBuildInfo;
    registerEventHandler(eventHandler: IRtcEngineEventHandler): boolean;
    unregisterEventHandler(eventHandler: IRtcEngineEventHandler): boolean;
    createMediaPlayer(): IMediaPlayer;
    destroyMediaPlayer(mediaPlayer: IMediaPlayer): number;
    createMediaRecorder(info: RecorderStreamInfo): IMediaRecorder;
    destroyMediaRecorder(mediaRecorder: IMediaRecorder): number;
    startDirectCdnStreaming(eventHandler: IDirectCdnStreamingEventHandler, publishUrl: string, options: DirectCdnStreamingMediaOptions): number;
    registerMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): number;
    unregisterMediaMetadataObserver(observer: IMetadataObserver, type: MetadataType): number;
    protected getApiTypeFromJoinChannel(token: string, channelId: string, uid: number, options: ChannelMediaOptions): string;
    protected getApiTypeFromLeaveChannel(options?: LeaveChannelOptions): string;
    protected getApiTypeFromLeaveChannelEx(connection: RtcConnection, options?: LeaveChannelOptions): string;
    protected getApiTypeFromSetClientRole(role: ClientRoleType, options?: ClientRoleOptions): string;
    protected getApiTypeFromEnableDualStreamMode(enabled: boolean, streamConfig?: SimulcastStreamConfig): string;
    protected getApiTypeFromSetDualStreamMode(mode: SimulcastStreamMode, streamConfig?: SimulcastStreamConfig): string;
    protected getApiTypeFromJoinChannelWithUserAccount(token: string, channelId: string, userAccount: string, options?: ChannelMediaOptions): string;
    getAudioDeviceManager(): IAudioDeviceManager;
    getVideoDeviceManager(): IVideoDeviceManager;
    getMediaEngine(): IMediaEngine;
    getMusicContentCenter(): IMusicContentCenter;
    getLocalSpatialAudioEngine(): ILocalSpatialAudioEngine;
    getH265Transcoder(): IH265Transcoder;
    getNativeHandle(): number;
    registerAudioEncodedFrameObserver(config: AudioEncodedFrameObserverConfig, observer: IAudioEncodedFrameObserver): number;
    unregisterAudioEncodedFrameObserver(observer: IAudioEncodedFrameObserver): number;
    registerAudioSpectrumObserver(observer: IAudioSpectrumObserver): number;
    unregisterAudioSpectrumObserver(observer: IAudioSpectrumObserver): number;
}
//# sourceMappingURL=RtcEngineExInternal.d.ts.map