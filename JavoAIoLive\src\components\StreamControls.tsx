import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
} from 'react-native';

interface StreamControlsProps {
  isStreaming: boolean;
  isMuted: boolean;
  isVideoEnabled: boolean;
  onToggleMute: () => void;
  onToggleVideo: () => void;
  onSwitchCamera: () => void;
  onEndStream: () => void;
  viewerCount?: number;
}

const StreamControls: React.FC<StreamControlsProps> = ({
  isStreaming,
  isMuted,
  isVideoEnabled,
  onToggleMute,
  onToggleVideo,
  onSwitchCamera,
  onEndStream,
  viewerCount = 0,
}) => {
  console.log('StreamControls render - isStreaming:', isStreaming, 'viewerCount:', viewerCount);

  const handleEndStream = () => {
    Alert.alert(
      'End Live Stream',
      'Are you sure you want to end the live stream?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'End Stream', style: 'destructive', onPress: onEndStream },
      ]
    );
  };

  // Temporarily always render for debugging
  console.log('StreamControls rendering controls - isStreaming:', isStreaming);

  return (
    <View style={styles.container}>
      {/* MASSIVE DEBUG OVERLAY FOR STREAM CONTROLS */}
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: '#00ff00',
        padding: 15,
        zIndex: 99999,
        borderWidth: 5,
        borderColor: '#ff0000',
      }}>
        <Text style={{ color: '#000', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
          🎮 STREAM CONTROLS COMPONENT 🎮
        </Text>
        <Text style={{ color: '#000', fontSize: 12, textAlign: 'center' }}>
          isStreaming: {isStreaming ? 'YES' : 'NO'} | Viewers: {viewerCount}
        </Text>
      </View>
      {/* Stream Status */}
      <View style={styles.statusContainer}>
        <View style={styles.liveIndicator}>
          <Text style={styles.liveText}>🔴 LIVE</Text>
        </View>
        <Text style={styles.viewerCount}>👥 {viewerCount} viewers</Text>
      </View>

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        {/* Mute/Unmute Audio */}
        <TouchableOpacity
          style={[styles.controlButton, isMuted && styles.mutedButton]}
          onPress={onToggleMute}
        >
          <Text style={styles.controlIcon}>
            {isMuted ? '🔇' : '🎤'}
          </Text>
          <Text style={styles.controlLabel}>
            {isMuted ? 'Unmute' : 'Mute'}
          </Text>
        </TouchableOpacity>

        {/* Enable/Disable Video */}
        <TouchableOpacity
          style={[styles.controlButton, !isVideoEnabled && styles.disabledButton]}
          onPress={onToggleVideo}
        >
          <Text style={styles.controlIcon}>
            {isVideoEnabled ? '📹' : '📷'}
          </Text>
          <Text style={styles.controlLabel}>
            {isVideoEnabled ? 'Video On' : 'Video Off'}
          </Text>
        </TouchableOpacity>

        {/* Switch Camera */}
        <TouchableOpacity
          style={styles.controlButton}
          onPress={onSwitchCamera}
        >
          <Text style={styles.controlIcon}>🔄</Text>
          <Text style={styles.controlLabel}>Flip</Text>
        </TouchableOpacity>

        {/* End Stream */}
        <TouchableOpacity
          style={[styles.controlButton, styles.endButton]}
          onPress={handleEndStream}
        >
          <Text style={styles.controlIcon}>⏹️</Text>
          <Text style={styles.controlLabel}>End</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20, // Add some bottom margin to avoid being hidden
    left: 0,
    right: 0,
    backgroundColor: '#ff0000', // Bright red for debugging
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    borderWidth: 3,
    borderColor: '#00ff00', // Green border for visibility
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  liveIndicator: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  liveText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewerCount: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 50,
    width: 60,
    height: 60,
    marginHorizontal: 8,
  },
  mutedButton: {
    backgroundColor: 'rgba(255, 68, 68, 0.8)',
  },
  disabledButton: {
    backgroundColor: 'rgba(128, 128, 128, 0.8)',
  },
  endButton: {
    backgroundColor: 'rgba(255, 68, 68, 0.8)',
  },
  controlIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  controlLabel: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default StreamControls;
