# JavoAIo Live Contractor Hub

A React Native live streaming app for home improvement and contractor demonstrations, built with Expo.

## 🚀 Project Overview

This is a live streaming mobile application designed for DIY enthusiasts, home improvement experts, contractors, and interior designers to share their expertise through real-time video broadcasts.

### Key Features (MVP)
- ✅ User Authentication (Login/Register screens)
- ✅ Live Stream Broadcasting interface
- ✅ Live Stream Viewing with real-time chat
- ✅ Home screen with active streams list
- ✅ User Profile management
- ✅ Navigation between screens

### Tech Stack
- **Framework**: React Native with Expo
- **Navigation**: React Navigation
- **Live Streaming**: Agora.io (to be integrated)
- **Backend**: Firebase (to be configured)
- **Video Analytics**: Mux (to be integrated)
- **Language**: TypeScript

## 📱 Current Status

### ✅ Completed
1. **Project Setup**
   - Expo React Native project initialized
   - TypeScript configuration
   - Development server running
   - Basic project structure created

2. **Navigation System**
   - Stack and Tab navigation setup
   - Authentication flow
   - Main app flow with bottom tabs

3. **Core Screens**
   - Login Screen with form validation
   - Register Screen with form validation
   - Home Screen with live streams list
   - Live Stream Screen (broadcaster interface)
   - View Stream Screen (viewer interface with chat)
   - Profile Screen with user info and settings

4. **Project Structure**
   ```
   src/
   ├── screens/          # All app screens
   ├── components/       # Reusable UI components
   ├── navigation/       # Navigation configuration
   ├── services/         # API and service integrations
   ├── types/           # TypeScript type definitions
   └── utils/           # Constants and helper functions
   ```

5. **Configuration**
   - App metadata configured for "JavoAIo Live Contractor Hub"
   - Bundle identifiers set: `com.javoaio.livecontractorhub`
   - Required permissions for camera, microphone, and network access
   - EAS build configuration for development builds

### 🔄 Next Steps (To Be Implemented)

1. **Firebase Integration**
   - Set up Firebase project
   - Configure authentication
   - Set up Firestore for real-time data
   - Implement cloud functions for token generation

2. **Agora SDK Integration**
   - Install and configure Agora React Native SDK
   - Implement live streaming functionality
   - Add camera preview and controls
   - Handle stream joining/leaving

3. **Real-time Features**
   - Live chat with Firestore
   - Real-time viewer count
   - Stream status updates

4. **Enhanced UI/UX**
   - Custom icons and branding
   - Improved styling and animations
   - Loading states and error handling

## 🛠 Development Setup

### Prerequisites
- Node.js (v16 or later)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation
```bash
# Clone the repository
cd JavoAIoLive

# Install dependencies
npm install

# Start the development server
npm run web      # For web development
npm run android  # For Android (requires Android Studio)
npm run ios      # For iOS (requires macOS and Xcode)
```

### Available Scripts
- `npm start` - Start Expo development server
- `npm run web` - Start web development server
- `npm run android` - Start Android development
- `npm run ios` - Start iOS development

## 📋 Environment Variables (To Be Configured)

Create a `.env` file with the following variables:

```env
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id

# Agora Configuration
EXPO_PUBLIC_AGORA_APP_ID=your_agora_app_id

# Mux Configuration (for backend use)
EXPO_PUBLIC_MUX_PLAYBACK_ID=your_mux_playback_id
```

## 🏗 Build Configuration

The project is configured for EAS Build with three build profiles:

- **development**: For development builds with dev client
- **preview**: For internal testing
- **production**: For app store releases

To build:
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Build for development
eas build --profile development --platform android
eas build --profile development --platform ios
```

## 📱 App Configuration

- **App Name**: JavoAIo Live Contractor Hub
- **Bundle ID**: com.javoaio.livecontractorhub
- **Domain**: javoaio.com
- **Target Platforms**: iOS, Android, Web

## 🔐 Permissions

The app requests the following permissions:
- Camera access (for live streaming)
- Microphone access (for audio streaming)
- Internet access (for streaming and data)
- Network state access (for connection monitoring)
- Audio settings modification (for stream quality)

## 📞 Support

For development questions or issues, refer to:
- [Expo Documentation](https://docs.expo.dev/)
- [React Navigation Documentation](https://reactnavigation.org/)
- [Agora Documentation](https://docs.agora.io/)
- [Firebase Documentation](https://firebase.google.com/docs)

---

**Status**: Initial build complete ✅  
**Next Phase**: Firebase and Agora integration  
**Version**: 1.0.0-alpha
