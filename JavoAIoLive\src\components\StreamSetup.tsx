import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';

interface StreamSetupProps {
  onStartStream: (config: StreamConfig) => void;
  isLoading?: boolean;
}

export interface StreamConfig {
  title: string;
  description: string;
  isPrivate: boolean;
  quality: 'low' | 'medium' | 'high' | 'ultra';
  enableChat: boolean;
  enableRecording: boolean;
  category: string;
  tags: string[];
}

const StreamSetup: React.FC<StreamSetupProps> = ({
  onStartStream,
  isLoading = false,
}) => {
  const [config, setConfig] = useState<StreamConfig>({
    title: '',
    description: '',
    isPrivate: false,
    quality: 'high',
    enableChat: true,
    enableRecording: false,
    category: 'General',
    tags: [],
  });

  const [tagInput, setTagInput] = useState('');

  const qualityOptions = [
    { value: 'low', label: 'Low (480p)', bitrate: '500 kbps' },
    { value: 'medium', label: 'Medium (720p)', bitrate: '1000 kbps' },
    { value: 'high', label: 'High (1080p)', bitrate: '2000 kbps' },
    { value: 'ultra', label: 'Ultra (1440p)', bitrate: '4000 kbps' },
  ];

  const categories = [
    'General', 'Gaming', 'Music', 'Education', 'Technology',
    'Art', 'Sports', 'Cooking', 'Travel', 'Business'
  ];

  const handleAddTag = () => {
    if (tagInput.trim() && !config.tags.includes(tagInput.trim())) {
      setConfig(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setConfig(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleStartStream = () => {
    if (!config.title.trim()) {
      Alert.alert('Error', 'Please enter a stream title');
      return;
    }

    if (config.title.length < 3) {
      Alert.alert('Error', 'Stream title must be at least 3 characters long');
      return;
    }

    onStartStream(config);
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Debug Overlay for StreamSetup */}
      <View style={{
        position: 'absolute',
        top: 50,
        left: 10,
        backgroundColor: '#00ff00',
        padding: 8,
        borderRadius: 5,
        zIndex: 10000,
        borderWidth: 2,
        borderColor: '#ff0000',
      }}>
        <Text style={{ color: '#000', fontSize: 12, fontWeight: 'bold' }}>
          STREAMSETUP DEBUG
        </Text>
        <Text style={{ color: '#000', fontSize: 10 }}>
          Loading: {isLoading ? 'Y' : 'N'}
        </Text>
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📺 Stream Details</Text>

        {/* Title */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Title *</Text>
          <TextInput
            style={styles.textInput}
            value={config.title}
            onChangeText={(text) => setConfig(prev => ({ ...prev, title: text }))}
            placeholder="Enter your stream title..."
            placeholderTextColor="#999"
            maxLength={100}
          />
          <Text style={styles.charCount}>{config.title.length}/100</Text>
        </View>

        {/* Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.textInput, styles.textArea]}
            value={config.description}
            onChangeText={(text) => setConfig(prev => ({ ...prev, description: text }))}
            placeholder="Describe what you'll be streaming..."
            placeholderTextColor="#999"
            multiline
            numberOfLines={3}
            maxLength={500}
          />
          <Text style={styles.charCount}>{config.description.length}/500</Text>
        </View>

        {/* Category */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.categoryContainer}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    config.category === category && styles.categoryButtonActive
                  ]}
                  onPress={() => setConfig(prev => ({ ...prev, category }))}
                >
                  <Text style={[
                    styles.categoryText,
                    config.category === category && styles.categoryTextActive
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Tags */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tags</Text>
          <View style={styles.tagInputContainer}>
            <TextInput
              style={[styles.textInput, styles.tagInput]}
              value={tagInput}
              onChangeText={setTagInput}
              placeholder="Add tags..."
              placeholderTextColor="#999"
              onSubmitEditing={handleAddTag}
            />
            <TouchableOpacity style={styles.addTagButton} onPress={handleAddTag}>
              <Text style={styles.addTagText}>+</Text>
            </TouchableOpacity>
          </View>

          {config.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {config.tags.map((tag, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.tag}
                  onPress={() => handleRemoveTag(tag)}
                >
                  <Text style={styles.tagText}>{tag}</Text>
                  <Text style={styles.tagRemove}>×</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>⚙️ Stream Settings</Text>

        {/* Quality */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Video Quality</Text>
          {qualityOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.qualityOption,
                config.quality === option.value && styles.qualityOptionActive
              ]}
              onPress={() => setConfig(prev => ({ ...prev, quality: option.value as any }))}
            >
              <View style={styles.qualityInfo}>
                <Text style={[
                  styles.qualityLabel,
                  config.quality === option.value && styles.qualityLabelActive
                ]}>
                  {option.label}
                </Text>
                <Text style={[
                  styles.qualityBitrate,
                  config.quality === option.value && styles.qualityBitrateActive
                ]}>
                  {option.bitrate}
                </Text>
              </View>
              <View style={[
                styles.radioButton,
                config.quality === option.value && styles.radioButtonActive
              ]} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Switches */}
        <View style={styles.switchContainer}>
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>🔒 Private Stream</Text>
            <Switch
              value={config.isPrivate}
              onValueChange={(value) => setConfig(prev => ({ ...prev, isPrivate: value }))}
              trackColor={{ false: '#767577', true: '#007AFF' }}
              thumbColor={config.isPrivate ? '#fff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>💬 Enable Chat</Text>
            <Switch
              value={config.enableChat}
              onValueChange={(value) => setConfig(prev => ({ ...prev, enableChat: value }))}
              trackColor={{ false: '#767577', true: '#007AFF' }}
              thumbColor={config.enableChat ? '#fff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>📹 Record Stream</Text>
            <Switch
              value={config.enableRecording}
              onValueChange={(value) => setConfig(prev => ({ ...prev, enableRecording: value }))}
              trackColor={{ false: '#767577', true: '#007AFF' }}
              thumbColor={config.enableRecording ? '#fff' : '#f4f3f4'}
            />
          </View>
        </View>
      </View>

      {/* Start Button */}
      <TouchableOpacity
        style={[styles.startButton, isLoading && styles.startButtonDisabled]}
        onPress={handleStartStream}
        disabled={isLoading}
      >
        <Text style={styles.startButtonText}>
          {isLoading ? '🔄 Starting Stream...' : '🚀 Start Live Stream'}
        </Text>
      </TouchableOpacity>

      <View style={styles.bottomPadding} />
    </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  categoryContainer: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: '#007AFF',
  },
  categoryText: {
    fontSize: 14,
    color: '#333',
  },
  categoryTextActive: {
    color: '#fff',
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagInput: {
    flex: 1,
    marginRight: 8,
  },
  addTagButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addTagText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e0e0e0',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    color: '#333',
    marginRight: 4,
  },
  tagRemove: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  qualityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  qualityOptionActive: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  qualityInfo: {
    flex: 1,
  },
  qualityLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  qualityLabelActive: {
    color: '#007AFF',
  },
  qualityBitrate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  qualityBitrateActive: {
    color: '#007AFF',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  radioButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#007AFF',
  },
  switchContainer: {
    marginTop: 8,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  startButton: {
    backgroundColor: '#007AFF',
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  startButtonDisabled: {
    backgroundColor: '#ccc',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  bottomPadding: {
    height: 20,
  },
});

export default StreamSetup;
