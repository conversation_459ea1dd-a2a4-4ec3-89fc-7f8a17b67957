{"name": "expo-media-library", "version": "17.1.7", "description": "Provides access to user's media library.", "main": "build/MediaLibrary.js", "types": "build/MediaLibrary.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "media", "library", "cameraroll", "photos", "videos"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-media-library"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/media-library/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "codegenConfig": {"name": "expo-media-library", "type": "modules", "jsSrcsDir": "./src", "ios": {"modulesConformingToProtocol": {"RCTImageURLLoader": "MediaLibraryImageLoader"}}}, "gitHead": "7638c800b57fe78f57cc7f129022f58e84a523c5"}