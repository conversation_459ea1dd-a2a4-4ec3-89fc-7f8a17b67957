{"expo": {"name": "JavoAIo Live Contractor Hu<PERSON>", "slug": "javoaio-live-contractor-hub", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.javoaio.livecontractorhub"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.javoaio.livecontractorhub", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-dev-client", {"addGeneratedScheme": false}], ["expo-camera", {"cameraPermission": "Allow JavoAIo Live to access your camera for live streaming.", "microphonePermission": "Allow JavoAIo Live to access your microphone for live streaming.", "recordAudioAndroid": true}]], "extra": {"eas": {"projectId": "77558172-6d3c-475c-9280-c48586f9458a"}}}}