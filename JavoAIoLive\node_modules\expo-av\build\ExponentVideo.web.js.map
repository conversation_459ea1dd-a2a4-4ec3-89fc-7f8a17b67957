{"version": 3, "file": "ExponentVideo.web.js", "sourceRoot": "", "sources": ["../src/ExponentVideo.web.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAGxE,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAC9D,OAAO,EACL,qBAAqB,GAGtB,MAAM,eAAe,CAAC;AA2BvB,MAAM,KAAK,GAAQ,KAAK,CAAC,UAAU,CAAuC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CACvF,aAAa,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAC1C,CAAC;AAEF,MAAM,CAAC,OAAO,OAAO,aAAc,SAAQ,KAAK,CAAC,SAA6B;IAC5E,MAAM,CAAoB;IAC1B,yBAAyB,CAAa;IAEtC,oBAAoB;QAClB,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;IACrC,CAAC;IAED,eAAe,GAAG,GAAG,EAAE;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC,CAAC;IAEF,kBAAkB,GAAG,CAAC,YAAqB,EAAE,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB;YAAE,OAAO;QAC3C,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBAC5B,WAAW,EAAE,EAAE,gBAAgB,EAAE,qBAAqB,CAAC,kBAAkB,EAAE;aAC5E,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;gBAC5B,WAAW,EAAE,EAAE,gBAAgB,EAAE,qBAAqB,CAAC,kBAAkB,EAAE;aAC5E,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;IAEF,cAAc,GAAG,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,WAAW,GAAG,GAAG,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,YAAY,GAAG,CAAC,KAAwC,EAAE,EAAE;QAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,GAAG,CAAC,KAAyC,EAAE,EAAE;QACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,UAAU,GAAG,GAAG,EAAE;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,SAAS,GAAG,GAAG,EAAE;QACf,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,OAAO,GAAG,GAAG,EAAE;QACb,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,gBAAgB,GAAG,GAAG,EAAE;QACtB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,SAAS,GAAG,CAAC,KAAiD,EAAE,EAAE;QAChE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,SAAS,GAAG,GAAG,EAAE;QACf,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC,CAAC;IAEF,KAAK,GAAG,CAAC,GAA4B,EAAE,EAAE;QACvC,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC;QACnC,IAAI,GAAG,EAAE,CAAC;YACR,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;YAClB,IAAI,CAAC,yBAAyB,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7F,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;QAC7C,CAAC;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAE5F,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,SAAS;YACnB,SAAS;YACT,QAAQ,EAAE,QAAQ;SACnB,CAAC;QACF,OAAO,CACL,CAAC,KAAK,CACJ,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAChB,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAC9B,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAChC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CACtB,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAC9B,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CACtB,gBAAgB,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACxC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAC1B,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,IAAI,SAAS,CAAC,CAC9B,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CACtB,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CACvB,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAC5B,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAC5B,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAC5B,WAAW,EACX,CACH,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["import * as React from 'react';\nimport { ViewProps } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport { AVPlaybackNativeSource, AVPlaybackStatus, AVPlaybackStatusToSet } from './AV';\nimport ExponentAV from './ExponentAV';\nimport { addFullscreenListener } from './FullscreenUtils.web';\nimport {\n  VideoFullscreenUpdate,\n  VideoFullscreenUpdateEvent,\n  VideoReadyForDisplayEvent,\n} from './Video.types';\n\ntype ExponentVideoProps = {\n  source: AVPlaybackNativeSource | null;\n  resizeMode?: object;\n  status?: AVPlaybackStatusToSet;\n  useNativeControls?: boolean;\n  onStatusUpdate?: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onReadyForDisplay?: (event: { nativeEvent: VideoReadyForDisplayEvent }) => void;\n  onFullscreenUpdate?: (event: { nativeEvent: VideoFullscreenUpdateEvent }) => void;\n  onLoadStart: () => void;\n  onLoad: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onError: (event: { nativeEvent: { error: string } }) => void;\n  // Required by react-native\n  scaleX?: number;\n  scaleY?: number;\n  translateX?: number;\n  translateY?: number;\n  rotation?: number;\n} & ViewProps;\n\nexport type NaturalSize = {\n  width: number;\n  height: number;\n  orientation: 'portrait' | 'landscape';\n};\n\nconst Video: any = React.forwardRef<HTMLVideoElement, ExponentVideoProps>((props, ref) =>\n  createElement('video', { ...props, ref })\n);\n\nexport default class ExponentVideo extends React.Component<ExponentVideoProps> {\n  _video?: HTMLVideoElement;\n  _removeFullscreenListener?: () => any;\n\n  componentWillUnmount() {\n    this._removeFullscreenListener?.();\n  }\n\n  getVideoElement = () => {\n    return this._video;\n  };\n\n  onFullscreenChange = (isFullscreen: boolean) => {\n    if (!this.props.onFullscreenUpdate) return;\n    if (isFullscreen) {\n      this.props.onFullscreenUpdate({\n        nativeEvent: { fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_PRESENT },\n      });\n    } else {\n      this.props.onFullscreenUpdate({\n        nativeEvent: { fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_DISMISS },\n      });\n    }\n  };\n\n  onStatusUpdate = async () => {\n    if (!this.props.onStatusUpdate) {\n      return;\n    }\n    const nativeEvent = await ExponentAV.getStatusForVideo(this._video);\n    this.props.onStatusUpdate({ nativeEvent });\n  };\n\n  onLoadStart = () => {\n    if (!this.props.onLoadStart) {\n      return;\n    }\n    this.props.onLoadStart();\n    this.onStatusUpdate();\n  };\n\n  onLoadedData = (event: { nativeEvent: AVPlaybackStatus }) => {\n    if (!this.props.onLoad) {\n      return;\n    }\n    this.props.onLoad(event);\n    this.onStatusUpdate();\n  };\n\n  onError = (event: { nativeEvent: { error: string } }) => {\n    if (!this.props.onError) {\n      return;\n    }\n    this.props.onError(event);\n    this.onStatusUpdate();\n  };\n\n  onProgress = () => {\n    this.onStatusUpdate();\n  };\n\n  onSeeking = () => {\n    this.onStatusUpdate();\n  };\n\n  onEnded = () => {\n    this.onStatusUpdate();\n  };\n\n  onLoadedMetadata = () => {\n    this.onStatusUpdate();\n  };\n\n  onCanPlay = (event: { nativeEvent: VideoReadyForDisplayEvent }) => {\n    if (!this.props.onReadyForDisplay) {\n      return;\n    }\n    this.props.onReadyForDisplay(event);\n    this.onStatusUpdate();\n  };\n\n  onStalled = () => {\n    this.onStatusUpdate();\n  };\n\n  onRef = (ref: HTMLVideoElement | null) => {\n    this._removeFullscreenListener?.();\n    if (ref) {\n      this._video = ref;\n      this._removeFullscreenListener = addFullscreenListener(this._video, this.onFullscreenChange);\n      this.onStatusUpdate();\n    } else {\n      this._removeFullscreenListener = undefined;\n    }\n  };\n\n  render() {\n    const { source, status = {}, resizeMode: objectFit, useNativeControls, style } = this.props;\n\n    const customStyle = {\n      position: undefined,\n      objectFit,\n      overflow: 'hidden',\n    };\n    return (\n      <Video\n        ref={this.onRef}\n        onLoadStart={this.onLoadStart}\n        onLoadedData={this.onLoadedData}\n        onError={this.onError}\n        onTimeUpdate={this.onProgress}\n        onSeeking={this.onSeeking}\n        onEnded={this.onEnded}\n        onLoadedMetadata={this.onLoadedMetadata}\n        onCanPlay={this.onCanPlay}\n        onStalled={this.onStalled}\n        src={source?.uri || undefined}\n        muted={status.isMuted}\n        loop={status.isLooping}\n        autoPlay={status.shouldPlay}\n        controls={useNativeControls}\n        style={[style, customStyle]}\n        playsInline\n      />\n    );\n  }\n}\n"]}