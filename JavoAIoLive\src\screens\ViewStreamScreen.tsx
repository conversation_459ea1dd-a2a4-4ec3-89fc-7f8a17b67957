import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList, ChatMessage } from '../types';

type ViewStreamScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ViewStream'>;
type ViewStreamScreenRouteProp = RouteProp<RootStackParamList, 'ViewStream'>;

interface Props {
  navigation: ViewStreamScreenNavigationProp;
  route: ViewStreamScreenRouteProp;
}

const ViewStreamScreen: React.FC<Props> = ({ navigation, route }) => {
  const { streamId } = route.params;
  const [chatMessage, setChatMessage] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [viewerCount, setViewerCount] = useState(45);

  // Mock chat messages
  const mockMessages: ChatMessage[] = [
    {
      id: '1',
      streamId,
      userId: 'user1',
      username: 'DIY_Mike',
      message: 'Great tips! Thanks for sharing',
      timestamp: new Date(),
    },
    {
      id: '2',
      streamId,
      userId: 'user2',
      username: 'HomeBuilder_Sarah',
      message: 'What tools do you recommend for beginners?',
      timestamp: new Date(),
    },
  ];

  useEffect(() => {
    // TODO: Load stream data and join Agora channel
    setChatMessages(mockMessages);
  }, [streamId]);

  const sendMessage = () => {
    if (!chatMessage.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      streamId,
      userId: 'currentUser',
      username: 'You',
      message: chatMessage,
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, newMessage]);
    setChatMessage('');
  };

  const renderChatMessage = ({ item }: { item: ChatMessage }) => (
    <View style={styles.chatMessage}>
      <Text style={styles.chatUsername}>{item.username}</Text>
      <Text style={styles.chatText}>{item.message}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Video Area */}
      <View style={styles.videoContainer}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        
        <View style={styles.videoPlaceholder}>
          <Text style={styles.videoText}>Live Stream Video</Text>
          <Text style={styles.videoSubtext}>Agora video player will appear here</Text>
        </View>
        
        <View style={styles.streamInfo}>
          <Text style={styles.viewerCountText}>👥 {viewerCount} viewers</Text>
        </View>
      </View>

      {/* Chat Area */}
      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.chatHeader}>
          <Text style={styles.chatTitle}>Live Chat</Text>
        </View>
        
        <FlatList
          data={chatMessages}
          renderItem={renderChatMessage}
          keyExtractor={(item) => item.id}
          style={styles.chatList}
          showsVerticalScrollIndicator={false}
        />
        
        <View style={styles.chatInput}>
          <TextInput
            style={styles.messageInput}
            placeholder="Type a message..."
            value={chatMessage}
            onChangeText={setChatMessage}
            multiline
            maxLength={200}
          />
          <TouchableOpacity style={styles.sendButton} onPress={sendMessage}>
            <Text style={styles.sendButtonText}>Send</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoContainer: {
    flex: 2,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333',
  },
  videoText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  videoSubtext: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 8,
  },
  streamInfo: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  viewerCountText: {
    color: '#fff',
    fontSize: 14,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  chatHeader: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  chatTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  chatList: {
    flex: 1,
    paddingHorizontal: 15,
  },
  chatMessage: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  chatUsername: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 2,
  },
  chatText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 18,
  },
  chatInput: {
    flexDirection: 'row',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'flex-end',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    maxHeight: 80,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ViewStreamScreen;
