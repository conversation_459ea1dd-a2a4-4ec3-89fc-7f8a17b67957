// @ts-ignore
export class IAudioDeviceManagerImpl {
  enumeratePlaybackDevices() {
    const apiType = this.getApiTypeFromEnumeratePlaybackDevices();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromEnumeratePlaybackDevices() {
    return 'AudioDeviceManager_enumeratePlaybackDevices';
  }
  enumerateRecordingDevices() {
    const apiType = this.getApiTypeFromEnumerateRecordingDevices();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromEnumerateRecordingDevices() {
    return 'AudioDeviceManager_enumerateRecordingDevices';
  }
  setPlaybackDevice(deviceId) {
    const apiType = this.getApiTypeFromSetPlaybackDevice(deviceId);
    const jsonParams = {
      deviceId: deviceId,
      toJSON: () => {
        return {
          deviceId: deviceId
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetPlaybackDevice(deviceId) {
    return 'AudioDeviceManager_setPlaybackDevice_4ad5f6e';
  }
  getPlaybackDevice() {
    const apiType = this.getApiTypeFromGetPlaybackDevice();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const deviceId = jsonResults.deviceId;
    return deviceId;
  }
  getApiTypeFromGetPlaybackDevice() {
    return 'AudioDeviceManager_getPlaybackDevice_73b9872';
  }
  getPlaybackDeviceInfo() {
    const apiType = this.getApiTypeFromGetPlaybackDeviceInfo();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromGetPlaybackDeviceInfo() {
    return 'AudioDeviceManager_getPlaybackDeviceInfo_ed3a96d';
  }
  setPlaybackDeviceVolume(volume) {
    const apiType = this.getApiTypeFromSetPlaybackDeviceVolume(volume);
    const jsonParams = {
      volume: volume,
      toJSON: () => {
        return {
          volume: volume
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetPlaybackDeviceVolume(volume) {
    return 'AudioDeviceManager_setPlaybackDeviceVolume_46f8ab7';
  }
  getPlaybackDeviceVolume() {
    const apiType = this.getApiTypeFromGetPlaybackDeviceVolume();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const volume = jsonResults.volume;
    return volume;
  }
  getApiTypeFromGetPlaybackDeviceVolume() {
    return 'AudioDeviceManager_getPlaybackDeviceVolume_915cb25';
  }
  setRecordingDevice(deviceId) {
    const apiType = this.getApiTypeFromSetRecordingDevice(deviceId);
    const jsonParams = {
      deviceId: deviceId,
      toJSON: () => {
        return {
          deviceId: deviceId
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetRecordingDevice(deviceId) {
    return 'AudioDeviceManager_setRecordingDevice_4ad5f6e';
  }
  getRecordingDevice() {
    const apiType = this.getApiTypeFromGetRecordingDevice();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const deviceId = jsonResults.deviceId;
    return deviceId;
  }
  getApiTypeFromGetRecordingDevice() {
    return 'AudioDeviceManager_getRecordingDevice_73b9872';
  }
  getRecordingDeviceInfo() {
    const apiType = this.getApiTypeFromGetRecordingDeviceInfo();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromGetRecordingDeviceInfo() {
    return 'AudioDeviceManager_getRecordingDeviceInfo_ed3a96d';
  }
  setRecordingDeviceVolume(volume) {
    const apiType = this.getApiTypeFromSetRecordingDeviceVolume(volume);
    const jsonParams = {
      volume: volume,
      toJSON: () => {
        return {
          volume: volume
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetRecordingDeviceVolume(volume) {
    return 'AudioDeviceManager_setRecordingDeviceVolume_46f8ab7';
  }
  getRecordingDeviceVolume() {
    const apiType = this.getApiTypeFromGetRecordingDeviceVolume();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const volume = jsonResults.volume;
    return volume;
  }
  getApiTypeFromGetRecordingDeviceVolume() {
    return 'AudioDeviceManager_getRecordingDeviceVolume_915cb25';
  }
  setLoopbackDevice(deviceId) {
    const apiType = this.getApiTypeFromSetLoopbackDevice(deviceId);
    const jsonParams = {
      deviceId: deviceId,
      toJSON: () => {
        return {
          deviceId: deviceId
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetLoopbackDevice(deviceId) {
    return 'AudioDeviceManager_setLoopbackDevice_4ad5f6e';
  }
  getLoopbackDevice() {
    const apiType = this.getApiTypeFromGetLoopbackDevice();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const deviceId = jsonResults.deviceId;
    return deviceId;
  }
  getApiTypeFromGetLoopbackDevice() {
    return 'AudioDeviceManager_getLoopbackDevice_73b9872';
  }
  setPlaybackDeviceMute(mute) {
    const apiType = this.getApiTypeFromSetPlaybackDeviceMute(mute);
    const jsonParams = {
      mute: mute,
      toJSON: () => {
        return {
          mute: mute
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetPlaybackDeviceMute(mute) {
    return 'AudioDeviceManager_setPlaybackDeviceMute_5039d15';
  }
  getPlaybackDeviceMute() {
    const apiType = this.getApiTypeFromGetPlaybackDeviceMute();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const mute = jsonResults.mute;
    return mute;
  }
  getApiTypeFromGetPlaybackDeviceMute() {
    return 'AudioDeviceManager_getPlaybackDeviceMute_d942327';
  }
  setRecordingDeviceMute(mute) {
    const apiType = this.getApiTypeFromSetRecordingDeviceMute(mute);
    const jsonParams = {
      mute: mute,
      toJSON: () => {
        return {
          mute: mute
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromSetRecordingDeviceMute(mute) {
    return 'AudioDeviceManager_setRecordingDeviceMute_5039d15';
  }
  getRecordingDeviceMute() {
    const apiType = this.getApiTypeFromGetRecordingDeviceMute();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    const mute = jsonResults.mute;
    return mute;
  }
  getApiTypeFromGetRecordingDeviceMute() {
    return 'AudioDeviceManager_getRecordingDeviceMute_d942327';
  }
  startPlaybackDeviceTest(testAudioFilePath) {
    const apiType = this.getApiTypeFromStartPlaybackDeviceTest(testAudioFilePath);
    const jsonParams = {
      testAudioFilePath: testAudioFilePath,
      toJSON: () => {
        return {
          testAudioFilePath: testAudioFilePath
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStartPlaybackDeviceTest(testAudioFilePath) {
    return 'AudioDeviceManager_startPlaybackDeviceTest_3a2037f';
  }
  stopPlaybackDeviceTest() {
    const apiType = this.getApiTypeFromStopPlaybackDeviceTest();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStopPlaybackDeviceTest() {
    return 'AudioDeviceManager_stopPlaybackDeviceTest';
  }
  startRecordingDeviceTest(indicationInterval) {
    const apiType = this.getApiTypeFromStartRecordingDeviceTest(indicationInterval);
    const jsonParams = {
      indicationInterval: indicationInterval,
      toJSON: () => {
        return {
          indicationInterval: indicationInterval
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStartRecordingDeviceTest(indicationInterval) {
    return 'AudioDeviceManager_startRecordingDeviceTest_46f8ab7';
  }
  stopRecordingDeviceTest() {
    const apiType = this.getApiTypeFromStopRecordingDeviceTest();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStopRecordingDeviceTest() {
    return 'AudioDeviceManager_stopRecordingDeviceTest';
  }
  startAudioDeviceLoopbackTest(indicationInterval) {
    const apiType = this.getApiTypeFromStartAudioDeviceLoopbackTest(indicationInterval);
    const jsonParams = {
      indicationInterval: indicationInterval,
      toJSON: () => {
        return {
          indicationInterval: indicationInterval
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStartAudioDeviceLoopbackTest(indicationInterval) {
    return 'AudioDeviceManager_startAudioDeviceLoopbackTest_46f8ab7';
  }
  stopAudioDeviceLoopbackTest() {
    const apiType = this.getApiTypeFromStopAudioDeviceLoopbackTest();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromStopAudioDeviceLoopbackTest() {
    return 'AudioDeviceManager_stopAudioDeviceLoopbackTest';
  }
  followSystemPlaybackDevice(enable) {
    const apiType = this.getApiTypeFromFollowSystemPlaybackDevice(enable);
    const jsonParams = {
      enable: enable,
      toJSON: () => {
        return {
          enable: enable
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromFollowSystemPlaybackDevice(enable) {
    return 'AudioDeviceManager_followSystemPlaybackDevice_5039d15';
  }
  followSystemRecordingDevice(enable) {
    const apiType = this.getApiTypeFromFollowSystemRecordingDevice(enable);
    const jsonParams = {
      enable: enable,
      toJSON: () => {
        return {
          enable: enable
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromFollowSystemRecordingDevice(enable) {
    return 'AudioDeviceManager_followSystemRecordingDevice_5039d15';
  }
  followSystemLoopbackDevice(enable) {
    const apiType = this.getApiTypeFromFollowSystemLoopbackDevice(enable);
    const jsonParams = {
      enable: enable,
      toJSON: () => {
        return {
          enable: enable
        };
      }
    };
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromFollowSystemLoopbackDevice(enable) {
    return 'AudioDeviceManager_followSystemLoopbackDevice_5039d15';
  }
  release() {
    const apiType = this.getApiTypeFromRelease();
    const jsonParams = {};
    callIrisApi.call(this, apiType, jsonParams);
  }
  getApiTypeFromRelease() {
    return 'AudioDeviceManager_release';
  }
  getPlaybackDefaultDevice() {
    const apiType = this.getApiTypeFromGetPlaybackDefaultDevice();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromGetPlaybackDefaultDevice() {
    return 'AudioDeviceManager_getPlaybackDefaultDevice';
  }
  getRecordingDefaultDevice() {
    const apiType = this.getApiTypeFromGetRecordingDefaultDevice();
    const jsonParams = {};
    const jsonResults = callIrisApi.call(this, apiType, jsonParams);
    return jsonResults.result;
  }
  getApiTypeFromGetRecordingDefaultDevice() {
    return 'AudioDeviceManager_getRecordingDefaultDevice';
  }
}
import { callIrisApi } from '../internal/IrisApiEngine';
//# sourceMappingURL=IAudioDeviceManagerImpl.js.map