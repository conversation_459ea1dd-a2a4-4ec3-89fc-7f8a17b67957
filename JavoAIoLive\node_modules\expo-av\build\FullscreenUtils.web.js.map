{"version": 3, "file": "FullscreenUtils.web.js", "sourceRoot": "", "sources": ["../src/FullscreenUtils.web.ts"], "names": [], "mappings": "AASA;;;;GAIG;AACH,MAAM,qBAAqB,GAAG,CAAC,OAAyB,EAAW,EAAE,CACnE,mBAAmB,IAAI,OAAO,CAAC;AAOjC;;;GAGG;AACH,MAAM,2BAA2B,GAAG,CAClC,OAAyB,EACW,EAAE,CAAC,uBAAuB,IAAI,OAAO,CAAC;AAM5E;;;GAGG;AACH,MAAM,uBAAuB,GAAG,CAAC,OAAyB,EAAkC,EAAE,CAC5F,qBAAqB,IAAI,OAAO,CAAC;AAEnC;;;;GAIG;AACH,MAAM,mCAAmC,GAAG,GAAY,EAAE,CACxD,aAAa,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAAC;AAEnD;;;;;GAKG;AACH,SAAS,gBAAgB,CACvB,OAA+B,EAC/B,SAAiB,EACjB,QAA4C;IAE5C,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC9C,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,MAAM,aAAa,GAAG,CAAC,WAAmB,EAAE,SAAiB,EAAW,EAAE;IACxE,0EAA0E;IAC1E,8EAA8E;IAC9E,wEAAwE;IACxE,4EAA4E;IAC5E,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACpD,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,SAAS,EAAE,SAAS,CAAC,CAAC;IAClD,OAAO,OAAO,OAAO,CAAC,CAAC,IAAI,GAAG,SAAS,CAAkB,CAAC,KAAK,UAAU,CAAC;AAC5E,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,OAAyB;IAC/D,IAAI,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC,iBAAiB,EAAE,CAAC;IACrC,CAAC;SAAM,IAAI,2BAA2B,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,0DAA0D;QAC1D,OAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC;IACvC,CAAC;SAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,0DAA0D;QAC1D,OAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,CAAC;IACrC,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC;AAED;;GAEG;AAEH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,OAAyB;IAC5D,IAAI,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,OAAO,QAAQ,CAAC,cAAc,EAAE,CAAC;IACnC,CAAC;SAAM,IAAI,2BAA2B,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,0DAA0D;QAC1D,OAAO,CAAC,sBAAsB,CAAC,EAAE,EAAE,CAAC;IACtC,CAAC;SAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,0DAA0D;QAC1D,QAAQ,CAAC,kBAAkB,CAAC,EAAE,EAAE,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CACnC,OAAyB,EACzB,QAAyC;IAEzC,IAAI,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,kDAAkD;QAClD,OAAO,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,KAAK,EAAE,EAAE,CAC7D,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,KAAK,KAAK,CAAC,MAAM,CAAC,CACtD,CAAC;IACJ,CAAC;SAAM,IAAI,2BAA2B,CAAC,OAAO,CAAC,IAAI,mCAAmC,EAAE,EAAE,CAAC;QACzF,0BAA0B;QAC1B,OAAO,gBAAgB,CAAC,OAAO,EAAE,wBAAwB,EAAE,CAAC,KAAK,EAAE,EAAE,CACnE,QAAQ,CAAC,QAAQ,CAAC,yBAAyB,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,CAC/D,CAAC;IACJ,CAAC;SAAM,IAAI,2BAA2B,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,wBAAwB;QACxB,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,OAAO,EAAE,uBAAuB,EAAE,GAAG,EAAE,CAClF,QAAQ,CAAC,IAAI,CAAC,CACf,CAAC;QACF,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,OAAO,EAAE,qBAAqB,EAAE,GAAG,EAAE,CAC9E,QAAQ,CAAC,KAAK,CAAC,CAChB,CAAC;QACF,OAAO,GAAG,EAAE;YACV,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;QACtB,CAAC,CAAC;IACJ,CAAC;SAAM,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,eAAe;QACf,OAAO,gBAAgB,CAAC,QAAQ,EAAE,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE,CAChE,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,CAC3D,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;IAClB,CAAC;AACH,CAAC", "sourcesContent": ["declare global {\n  interface Document {\n    fullscreenElement?: Element | null;\n    msFullscreenElement?: Element | null;\n    webkitFullscreenElement?: Element | null;\n    msExitFullscreen?(): void;\n  }\n}\n\n/**\n * Detect if the browser supports the standard fullscreen API on the given\n * element:\n * https://developer.mozilla.org/en-US/docs/Web/API/Fullscreen_API\n */\nconst supportsFullscreenAPI = (element: HTMLMediaElement): boolean =>\n  'requestFullscreen' in element;\n\ninterface WebkitFullscreenElement extends HTMLMediaElement {\n  webkitExitFullScreen?(): void;\n  webkitEnterFullScreen?(): void;\n}\n\n/**\n * Detect if the browser supports the non-standard webkit fullscreen API on the\n * given element (looking at you, <PERSON><PERSON>).\n */\nconst supportsWebkitFullscreenAPI = (\n  element: HTMLMediaElement\n): element is WebkitFullscreenElement => 'webkitEnterFullScreen' in element;\n\ninterface IEFullscreenElement extends HTMLMediaElement {\n  msRequestFullscreen?(): void;\n}\n\n/**\n * Detect if the browser supports the non-standard ms fullscreen API on the\n * given element (looking at you, IE11).\n */\nconst supportsMsFullscreenAPI = (element: HTMLMediaElement): element is IEFullscreenElement =>\n  'msRequestFullscreen' in element;\n\n/**\n * Detect if the browser supports the `webkitFullscreenChange` event. This is\n * a non-standard event added to Safari on macOS by Apple:\n * https://developer.apple.com/documentation/webkitjs/document/1631998-onwebkitfullscreenchange\n */\nconst supportsWebkitFullscreenChangeEvent = (): boolean =>\n  supportsEvent('video', 'webkitfullscreenchange');\n\n/**\n * A helper that adds an event listener to an element. The key value-add over\n * the native addEventListener is that it returns a function that will remove\n * the event listener. This allows the setup and teardown logic for a listener\n * to be easily colocated.\n */\nfunction addEventListener(\n  element: Document | HTMLElement,\n  eventName: string,\n  listener: EventListenerOrEventListenerObject\n): () => any {\n  element.addEventListener(eventName, listener);\n  return () => element.removeEventListener(eventName, listener);\n}\n\n/**\n * Detect if the browser supports an event on a particular element type.\n */\nconst supportsEvent = (elementName: string, eventName: string): boolean => {\n  // Detect if the browser supports the event by attempting to add a handler\n  // attribute for that event to the provided element. If the event is supported\n  // then the browser will accept the attribute and report the type of the\n  // attribute as \"function\". See: https://stackoverflow.com/a/4562426/2747759\n  const element = document.createElement(elementName);\n  element.setAttribute('on' + eventName, 'return;');\n  return typeof element[('on' + eventName) as keyof Element] === 'function';\n};\n\n/**\n * Switches a video element into fullscreen.\n */\nexport async function requestFullscreen(element: HTMLMediaElement): Promise<void> {\n  if (supportsFullscreenAPI(element)) {\n    return element.requestFullscreen();\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['webkitEnterFullScreen']?.();\n  } else if (supportsMsFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['msRequestFullscreen']?.();\n  } else {\n    throw new Error('Fullscreen not supported');\n  }\n}\n\n/**\n * Switches a video element out of fullscreen.\n */\n\nexport async function exitFullscreen(element: HTMLMediaElement): Promise<void> {\n  if (supportsFullscreenAPI(element)) {\n    return document.exitFullscreen();\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['webkitExitFullScreen']?.();\n  } else if (supportsMsFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    document['msExitFullscreen']?.();\n  } else {\n    throw new Error('Fullscreen not supported');\n  }\n}\n\n/**\n * Listens for fullscreen change events on a video element. The provided\n * callback will be called with `true` when the video is switched into\n * fullscreen and `false` when the video is switched out of fullscreen.\n */\nexport function addFullscreenListener(\n  element: HTMLVideoElement,\n  callback: (isFullscreen: boolean) => void\n): () => any {\n  if (supportsFullscreenAPI(element)) {\n    // Used by browsers that support the official spec\n    return addEventListener(element, 'fullscreenchange', (event) =>\n      callback(document.fullscreenElement === event.target)\n    );\n  } else if (supportsWebkitFullscreenAPI(element) && supportsWebkitFullscreenChangeEvent()) {\n    // Used by Safari on macOS\n    return addEventListener(element, 'webkitfullscreenchange', (event) =>\n      callback(document['webkitFullscreenElement'] === event.target)\n    );\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // Used by Safari on iOS\n    const removeBeginListener = addEventListener(element, 'webkitbeginfullscreen', () =>\n      callback(true)\n    );\n    const removeEndListener = addEventListener(element, 'webkitendfullscreen', () =>\n      callback(false)\n    );\n    return () => {\n      removeBeginListener();\n      removeEndListener();\n    };\n  } else if (supportsMsFullscreenAPI(element)) {\n    // Used by IE11\n    return addEventListener(document, 'MSFullscreenChange', (event) =>\n      callback(document['msFullscreenElement'] === event.target)\n    );\n  } else {\n    return () => {};\n  }\n}\n"]}