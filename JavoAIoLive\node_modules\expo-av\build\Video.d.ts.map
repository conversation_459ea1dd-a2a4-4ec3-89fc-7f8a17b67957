{"version": 3, "file": "Video.d.ts", "sourceRoot": "", "sources": ["../src/Video.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAyB,aAAa,EAA8B,MAAM,cAAc,CAAC;AAEhG,OAAO,EAKL,QAAQ,EAER,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACvB,MAAM,MAAM,CAAC;AAId,OAAO,EAEL,0BAA0B,EAC1B,gBAAgB,EAChB,UAAU,EACV,yBAAyB,EAEzB,UAAU,EACX,MAAM,eAAe,CAAC;AA+BvB,cAAM,KAAM,SAAQ,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,CAAE,YAAW,QAAQ;IAC7E,UAAU,wFAA2E;IACrF,uBAAuB,EAAE,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAQ;gBAEhE,KAAK,EAAE,UAAU;IAO7B;;OAEG;IACH,cAAc,CAAC,WAAW,EAAE,gBAAgB;IAQ5C,gBAAgB,GAAI,QAAQ,gBAAgB,UAe1C;IAEF,qCAAqC,GACnC,WAAW,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC,gBAAgB,CAAC,KACpD,OAAO,CAAC,gBAAgB,CAAC,CAoB1B;IAIF,cAAc,GAAU,OAAO,OAAO,KAAG,OAAO,CAAC,gBAAgB,CAAC,CAIhE;IAEF;;;;;OAKG;IACH,uBAAuB,QAAa,OAAO,CAAC,gBAAgB,CAAC,CAE3D;IAEF;;;;OAIG;IACH,uBAAuB,QAAa,OAAO,CAAC,gBAAgB,CAAC,CAE3D;IAKF;;OAEG;IACH,cAAc,QAAa,OAAO,CAAC,gBAAgB,CAAC,CAIlD;IAEF;;OAEG;IACH,SAAS,GACP,QAAQ,gBAAgB,EACxB,gBAAe,qBAA0B,EACzC,gBAAe,OAAc,KAC5B,OAAO,CAAC,gBAAgB,CAAC,CAM1B;IAEF;;;OAGG;IACH,WAAW,QAAa,OAAO,CAAC,gBAAgB,CAAC,CAI/C;IAEF,oBAAoB;IAUpB;;;OAGG;IACH,cAAc,GAAU,QAAQ,qBAAqB,KAAG,OAAO,CAAC,gBAAgB,CAAC,CAK/E;IAEF;;OAEG;IACH,WAAW,GAAU,SAAQ,qBAA0B,KAAG,OAAO,CAAC,gBAAgB,CAAC,CAYjF;IAEF;;;;;;;;;;;OAWG;IACH,yBAAyB,CAAC,sBAAsB,EAAE,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAI;IAM7F,SAAS,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC5C,qBAAqB,EAAG,CACtB,cAAc,EAAE,MAAM,EACtB,UAAU,CAAC,EAAE,mBAAmB,KAC7B,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,UAAU,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC7C,SAAS,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC5C,gBAAgB,EAAG,CACjB,cAAc,EAAE,MAAM,EACtB,UAAU,CAAC,EAAE,mBAAmB,KAC7B,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,YAAY,EAAG,CACb,IAAI,EAAE,MAAM,EACZ,kBAAkB,EAAE,OAAO,EAC3B,sBAAsB,CAAC,EAAE,sBAAsB,KAC5C,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,cAAc,EAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAClF,eAAe,EAAG,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAClE,iBAAiB,EAAG,CAAC,SAAS,EAAE,OAAO,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACtE,8BAA8B,EAAG,CAC/B,4BAA4B,EAAE,MAAM,KACjC,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAI/B,6BAA6B,GAAI,OAAO;QAAE,WAAW,EAAE,gBAAgB,CAAA;KAAE,UAEvE;IAGF,kBAAkB,aAIhB;IAEF,aAAa,GAAI,OAAO;QAAE,WAAW,EAAE,gBAAgB,CAAA;KAAE,UAKvD;IAEF,cAAc,GAAI,OAAO;QAAE,WAAW,EAAE;YAAE,KAAK,EAAE,MAAM,CAAA;SAAE,CAAA;KAAE,UAMzD;IAEF,wBAAwB,GAAI,OAAO;QAAE,WAAW,EAAE,yBAAyB,CAAA;KAAE,UAI3E;IAEF,yBAAyB,GAAI,OAAO;QAAE,WAAW,EAAE,0BAA0B,CAAA;KAAE,UAI7E;IAEF,aAAa,iCASX;IAEF,MAAM;CAkEP;AAuBD,eAAe,KAAK,CAAC"}