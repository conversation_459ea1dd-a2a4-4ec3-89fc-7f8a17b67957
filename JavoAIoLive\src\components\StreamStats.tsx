import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';

interface StreamStatsProps {
  isVisible: boolean;
  isStreaming: boolean;
  duration?: number; // in seconds
  bitrate?: number; // in kbps
  resolution?: string;
  fps?: number;
  networkQuality?: 'excellent' | 'good' | 'poor' | 'bad';
}

const StreamStats: React.FC<StreamStatsProps> = ({
  isVisible,
  isStreaming,
  duration = 0,
  bitrate = 0,
  resolution = '1080x1920',
  fps = 30,
  networkQuality = 'good',
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [streamDuration, setStreamDuration] = useState(duration);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: isVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isVisible, fadeAnim]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isStreaming) {
      interval = setInterval(() => {
        setStreamDuration(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isStreaming]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getNetworkQualityColor = (quality: string): string => {
    switch (quality) {
      case 'excellent': return '#4CAF50';
      case 'good': return '#8BC34A';
      case 'poor': return '#FF9800';
      case 'bad': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getNetworkQualityIcon = (quality: string): string => {
    switch (quality) {
      case 'excellent': return '📶';
      case 'good': return '📶';
      case 'poor': return '📶';
      case 'bad': return '📵';
      default: return '📶';
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.header}>
        <Text style={styles.title}>📊 Stream Statistics</Text>
      </View>

      <View style={styles.statsGrid}>
        {/* Duration */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>⏱️ Duration</Text>
          <Text style={styles.statValue}>{formatDuration(streamDuration)}</Text>
        </View>

        {/* Bitrate */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>📡 Bitrate</Text>
          <Text style={styles.statValue}>{bitrate} kbps</Text>
        </View>

        {/* Resolution */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>🖥️ Resolution</Text>
          <Text style={styles.statValue}>{resolution}</Text>
        </View>

        {/* FPS */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>🎬 FPS</Text>
          <Text style={styles.statValue}>{fps}</Text>
        </View>

        {/* Network Quality */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>
            {getNetworkQualityIcon(networkQuality)} Network
          </Text>
          <Text 
            style={[
              styles.statValue, 
              { color: getNetworkQualityColor(networkQuality) }
            ]}
          >
            {networkQuality.charAt(0).toUpperCase() + networkQuality.slice(1)}
          </Text>
        </View>

        {/* Status */}
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>📺 Status</Text>
          <Text style={[
            styles.statValue,
            { color: isStreaming ? '#4CAF50' : '#F44336' }
          ]}>
            {isStreaming ? 'Live' : 'Offline'}
          </Text>
        </View>
      </View>

      {/* Performance Indicators */}
      <View style={styles.performanceContainer}>
        <Text style={styles.performanceTitle}>Performance</Text>
        <View style={styles.performanceBar}>
          <View 
            style={[
              styles.performanceFill,
              { 
                width: `${Math.min(100, (bitrate / 2000) * 100)}%`,
                backgroundColor: getNetworkQualityColor(networkQuality)
              }
            ]} 
          />
        </View>
        <Text style={styles.performanceText}>
          {networkQuality === 'excellent' ? 'Excellent quality' :
           networkQuality === 'good' ? 'Good quality' :
           networkQuality === 'poor' ? 'Poor quality - consider reducing bitrate' :
           'Bad quality - check network connection'}
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    borderRadius: 12,
    padding: 16,
    minWidth: 200,
    maxWidth: 280,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  statsGrid: {
    marginBottom: 12,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statLabel: {
    color: '#ccc',
    fontSize: 12,
    flex: 1,
  },
  statValue: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'right',
  },
  performanceContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
    paddingTop: 12,
  },
  performanceTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  performanceBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    marginBottom: 6,
  },
  performanceFill: {
    height: '100%',
    borderRadius: 2,
  },
  performanceText: {
    color: '#ccc',
    fontSize: 10,
    textAlign: 'center',
  },
});

export default StreamStats;
