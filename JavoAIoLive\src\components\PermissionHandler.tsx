import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Linking,
  Platform,
} from 'react-native';
import PermissionsService from '../services/permissions';

interface PermissionHandlerProps {
  onPermissionsGranted: () => void;
  onPermissionsDenied: () => void;
  children?: React.ReactNode;
}

interface PermissionStatus {
  camera: boolean;
  microphone: boolean;
  isChecking: boolean;
  hasChecked: boolean;
}

const PermissionHandler: React.FC<PermissionHandlerProps> = ({
  onPermissionsGranted,
  onPermissionsDenied,
  children,
}) => {
  const [permissions, setPermissions] = useState<PermissionStatus>({
    camera: false,
    microphone: false,
    isChecking: false,
    hasChecked: false,
  });

  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    setPermissions(prev => ({ ...prev, isChecking: true }));

    try {
      const cameraStatus = await PermissionsService.checkCameraPermission();
      const microphoneStatus = await PermissionsService.checkMicrophonePermission();

      const newPermissions = {
        camera: cameraStatus === 'granted',
        microphone: microphoneStatus === 'granted',
        isChecking: false,
        hasChecked: true,
      };

      setPermissions(newPermissions);

      // Call appropriate callback
      if (newPermissions.camera && newPermissions.microphone) {
        onPermissionsGranted();
      } else {
        onPermissionsDenied();
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      setPermissions(prev => ({ 
        ...prev, 
        isChecking: false, 
        hasChecked: true 
      }));
      onPermissionsDenied();
    }
  };

  const requestPermissions = async () => {
    setPermissions(prev => ({ ...prev, isChecking: true }));

    try {
      const results = await PermissionsService.requestCameraAndMicrophonePermissions();
      
      const newPermissions = {
        camera: results.camera === 'granted',
        microphone: results.microphone === 'granted',
        isChecking: false,
        hasChecked: true,
      };

      setPermissions(newPermissions);

      if (newPermissions.camera && newPermissions.microphone) {
        onPermissionsGranted();
      } else {
        // Show detailed error message
        const deniedPermissions = [];
        if (!newPermissions.camera) deniedPermissions.push('Camera');
        if (!newPermissions.microphone) deniedPermissions.push('Microphone');

        Alert.alert(
          'Permissions Required',
          `${deniedPermissions.join(' and ')} access is required for live streaming. Please grant permissions in your device settings.`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: openSettings },
          ]
        );
        onPermissionsDenied();
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
      setPermissions(prev => ({ 
        ...prev, 
        isChecking: false 
      }));
      
      Alert.alert(
        'Permission Error',
        'Failed to request permissions. Please try again or check your device settings.',
        [
          { text: 'Retry', onPress: requestPermissions },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
      onPermissionsDenied();
    }
  };

  const openSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  };

  const getPermissionIcon = (granted: boolean, isChecking: boolean) => {
    if (isChecking) return '⏳';
    return granted ? '✅' : '❌';
  };

  const getPermissionText = (granted: boolean, isChecking: boolean) => {
    if (isChecking) return 'Checking...';
    return granted ? 'Granted' : 'Denied';
  };

  const allPermissionsGranted = permissions.camera && permissions.microphone;

  // If permissions are granted, render children
  if (allPermissionsGranted && permissions.hasChecked) {
    return <>{children}</>;
  }

  // Show permission request UI
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>📹 Camera & Microphone Access</Text>
        <Text style={styles.subtitle}>
          Live streaming requires access to your camera and microphone to broadcast video and audio.
        </Text>

        {/* Permission Status */}
        <View style={styles.permissionsList}>
          <View style={styles.permissionItem}>
            <Text style={styles.permissionIcon}>
              {getPermissionIcon(permissions.camera, permissions.isChecking)}
            </Text>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionName}>Camera</Text>
              <Text style={styles.permissionStatus}>
                {getPermissionText(permissions.camera, permissions.isChecking)}
              </Text>
            </View>
          </View>

          <View style={styles.permissionItem}>
            <Text style={styles.permissionIcon}>
              {getPermissionIcon(permissions.microphone, permissions.isChecking)}
            </Text>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionName}>Microphone</Text>
              <Text style={styles.permissionStatus}>
                {getPermissionText(permissions.microphone, permissions.isChecking)}
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {!permissions.hasChecked || permissions.isChecking ? (
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={checkPermissions}
              disabled={permissions.isChecking}
            >
              <Text style={styles.buttonText}>
                {permissions.isChecking ? '⏳ Checking...' : '🔍 Check Permissions'}
              </Text>
            </TouchableOpacity>
          ) : (
            <>
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={requestPermissions}
                disabled={permissions.isChecking}
              >
                <Text style={styles.buttonText}>
                  {permissions.isChecking ? '⏳ Requesting...' : '🔓 Grant Permissions'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={openSettings}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                  ⚙️ Open Settings
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>

        {/* Help Text */}
        <View style={styles.helpContainer}>
          <Text style={styles.helpTitle}>💡 Why do we need these permissions?</Text>
          <Text style={styles.helpText}>
            • <Text style={styles.helpBold}>Camera:</Text> To capture and broadcast your video
          </Text>
          <Text style={styles.helpText}>
            • <Text style={styles.helpBold}>Microphone:</Text> To capture and broadcast your audio
          </Text>
          <Text style={styles.helpNote}>
            Your privacy is important. We only access these features when you're actively streaming.
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  permissionsList: {
    width: '100%',
    marginBottom: 24,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 8,
  },
  permissionIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  permissionInfo: {
    flex: 1,
  },
  permissionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  permissionStatus: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 24,
  },
  button: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
  helpContainer: {
    width: '100%',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  helpText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 4,
  },
  helpBold: {
    fontWeight: '600',
    color: '#333',
  },
  helpNote: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 8,
    lineHeight: 16,
  },
});

export default PermissionHandler;
