import React from 'react';
import { ViewStyle, StyleProp, TextStyle } from 'react-native';
import { RneFunctionComponent } from '../helpers';
export declare type ButtonGroupProps = {
    button?: object;
    Component?: typeof React.Component;
    onPress?(...args: any[]): void;
    buttons?: (string | React.ReactElement<{}>)[];
    containerStyle?: StyleProp<ViewStyle>;
    textStyle?: StyleProp<TextStyle>;
    selectedTextStyle?: StyleProp<TextStyle>;
    selectedButtonStyle?: StyleProp<ViewStyle>;
    underlayColor?: string;
    selectedIndex?: number | null;
    selectedIndexes?: number[];
    activeOpacity?: number;
    onHideUnderlay?(): void;
    onShowUnderlay?(): void;
    setOpacityTo?: (value: number) => void;
    innerBorderStyle?: {
        color?: string;
        width?: number;
    };
    buttonStyle?: StyleProp<ViewStyle>;
    buttonContainerStyle?: StyleProp<ViewStyle>;
    selectMultiple?: boolean;
    disabled?: boolean | number[];
    disabledStyle?: StyleProp<ViewStyle>;
    disabledTextStyle?: StyleProp<TextStyle>;
    disabledSelectedStyle?: StyleProp<ViewStyle>;
    disabledSelectedTextStyle?: StyleProp<TextStyle>;
    vertical?: boolean;
};
declare const ButtonGroup: RneFunctionComponent<ButtonGroupProps>;
export { ButtonGroup };
declare const _default: React.FunctionComponent<Omit<ButtonGroupProps & Partial<import("../config").ThemeProps<ButtonGroupProps>>, keyof import("../config").ThemeProps<T>>> | React.ForwardRefExoticComponent<ButtonGroupProps & Partial<import("../config").ThemeProps<ButtonGroupProps>>>;
export default _default;
