import React from 'react';
import { TextStyle } from 'react-native';
import { TextProps } from '../text/Text';
declare const _default: React.FunctionComponent<Omit<import("react-native").TextProps & {
    style?: import("react-native").StyleProp<TextStyle>;
    h1?: boolean;
    h2?: boolean;
    h3?: boolean;
    h4?: boolean;
    h1Style?: import("react-native").StyleProp<TextStyle>;
    h2Style?: import("react-native").StyleProp<TextStyle>;
    h3Style?: import("react-native").StyleProp<TextStyle>;
    h4Style?: import("react-native").StyleProp<TextStyle>;
} & Partial<import("../config").ThemeProps<TextProps>>, keyof import("../config").ThemeProps<T>>> | React.ForwardRefExoticComponent<import("react-native").TextProps & {
    style?: import("react-native").StyleProp<TextStyle>;
    h1?: boolean;
    h2?: boolean;
    h3?: boolean;
    h4?: boolean;
    h1Style?: import("react-native").StyleProp<TextStyle>;
    h2Style?: import("react-native").StyleProp<TextStyle>;
    h3Style?: import("react-native").StyleProp<TextStyle>;
    h4Style?: import("react-native").StyleProp<TextStyle>;
} & Partial<import("../config").ThemeProps<TextProps>>>;
export default _default;
