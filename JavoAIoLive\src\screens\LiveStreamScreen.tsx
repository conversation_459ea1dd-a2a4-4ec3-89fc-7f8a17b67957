import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { RtcLocalView, RtcRemoteView } from 'react-native-agora';
import AgoraService, { AgoraEventHandlers } from '../services/agora';
import PermissionsService from '../services/permissions';
import { STREAM_SETTINGS } from '../utils/constants';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const LiveStreamScreen: React.FC = () => {
  const [streamTitle, setStreamTitle] = useState('');
  const [streamDescription, setStreamDescription] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isPreviewActive, setIsPreviewActive] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  const channelNameRef = useRef<string>('');
  const uidRef = useRef<number>(0);

  useEffect(() => {
    checkPermissions();
    return () => {
      cleanup();
    };
  }, []);

  const checkPermissions = async () => {
    try {
      const permissions = await PermissionsService.checkAllPermissions();
      setPermissionsGranted(permissions.granted);

      if (!permissions.granted) {
        PermissionsService.showPermissionsRequiredAlert();
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const requestPermissions = async () => {
    try {
      const permissions = await PermissionsService.requestAllPermissions();
      setPermissionsGranted(permissions.granted);
      return permissions.granted;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  };

  const initializeAgora = async () => {
    try {
      setIsInitializing(true);

      const eventHandlers: AgoraEventHandlers = {
        onJoinChannelSuccess: (channel, uid, elapsed) => {
          console.log('Successfully joined channel:', channel);
          setIsStreaming(true);
          setViewerCount(1); // Start with broadcaster
        },
        onUserJoined: (uid, elapsed) => {
          console.log('User joined:', uid);
          setViewerCount(prev => prev + 1);
        },
        onUserOffline: (uid, reason) => {
          console.log('User left:', uid);
          setViewerCount(prev => Math.max(0, prev - 1));
        },
        onError: (errorCode) => {
          console.error('Agora error:', errorCode);
          Alert.alert('Streaming Error', `Error code: ${errorCode}`);
        },
      };

      await AgoraService.initialize(eventHandlers);
      console.log('Agora initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Agora:', error);
      Alert.alert('Error', 'Failed to initialize streaming service');
    } finally {
      setIsInitializing(false);
    }
  };

  const startPreview = async () => {
    try {
      if (!permissionsGranted) {
        const granted = await requestPermissions();
        if (!granted) return;
      }

      if (!AgoraService.isInitialized()) {
        await initializeAgora();
      }

      await AgoraService.startPreview();
      setIsPreviewActive(true);
      console.log('Camera preview started');
    } catch (error) {
      console.error('Failed to start preview:', error);
      Alert.alert('Error', 'Failed to start camera preview');
    }
  };

  const stopPreview = async () => {
    try {
      await AgoraService.stopPreview();
      setIsPreviewActive(false);
      console.log('Camera preview stopped');
    } catch (error) {
      console.error('Failed to stop preview:', error);
    }
  };

  const startStream = async () => {
    if (!streamTitle.trim()) {
      Alert.alert('Error', 'Please enter a stream title');
      return;
    }

    if (!permissionsGranted) {
      const granted = await requestPermissions();
      if (!granted) return;
    }

    try {
      setIsInitializing(true);

      // Initialize Agora if not already done
      if (!AgoraService.isInitialized()) {
        await initializeAgora();
      }

      // Generate channel name and UID
      channelNameRef.current = `${STREAM_SETTINGS.defaultChannelPrefix}${Date.now()}`;
      uidRef.current = Math.floor(Math.random() * 100000);

      // Generate token (null for development)
      const token = await AgoraService.generateToken(channelNameRef.current, uidRef.current);

      // Start preview if not active
      if (!isPreviewActive) {
        await AgoraService.startPreview();
        setIsPreviewActive(true);
      }

      // Join channel as broadcaster
      await AgoraService.joinChannel(channelNameRef.current, token, uidRef.current, true);

      console.log('Live stream started successfully');
    } catch (error) {
      console.error('Failed to start stream:', error);
      Alert.alert('Error', 'Failed to start live stream');
      setIsStreaming(false);
    } finally {
      setIsInitializing(false);
    }
  };

  const stopStream = async () => {
    try {
      await AgoraService.leaveChannel();
      await stopPreview();
      setIsStreaming(false);
      setViewerCount(0);
      console.log('Live stream stopped');
    } catch (error) {
      console.error('Failed to stop stream:', error);
      Alert.alert('Error', 'Failed to stop stream');
    }
  };

  const toggleAudio = async () => {
    try {
      const newMutedState = !isAudioMuted;
      await AgoraService.muteLocalAudio(newMutedState);
      setIsAudioMuted(newMutedState);
    } catch (error) {
      console.error('Failed to toggle audio:', error);
    }
  };

  const toggleVideo = async () => {
    try {
      const newMutedState = !isVideoMuted;
      await AgoraService.muteLocalVideo(newMutedState);
      setIsVideoMuted(newMutedState);
    } catch (error) {
      console.error('Failed to toggle video:', error);
    }
  };

  const switchCamera = async () => {
    try {
      await AgoraService.switchCamera();
    } catch (error) {
      console.error('Failed to switch camera:', error);
    }
  };

  const cleanup = async () => {
    try {
      if (isStreaming) {
        await stopStream();
      }
      await AgoraService.destroy();
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Go Live</Text>
        <Text style={styles.headerSubtitle}>Share your expertise with the community</Text>
        {!permissionsGranted && (
          <Text style={styles.permissionWarning}>
            Camera and microphone permissions required
          </Text>
        )}
      </View>

      {!isStreaming ? (
        <View style={styles.setupContainer}>
          {/* Camera Preview Area */}
          <View style={styles.previewArea}>
            {isPreviewActive && AgoraService.isInitialized() ? (
              <RtcLocalView.SurfaceView
                style={styles.localVideo}
                channelId={channelNameRef.current}
                renderMode={1}
              />
            ) : (
              <View style={styles.previewPlaceholder}>
                <Text style={styles.previewText}>Camera Preview</Text>
                <Text style={styles.previewSubtext}>
                  {!permissionsGranted
                    ? 'Grant camera permission to see preview'
                    : 'Tap "Start Preview" to see camera feed'
                  }
                </Text>
                {permissionsGranted && !isPreviewActive && (
                  <TouchableOpacity style={styles.previewButton} onPress={startPreview}>
                    <Text style={styles.previewButtonText}>Start Preview</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Preview Controls */}
            {isPreviewActive && (
              <View style={styles.previewControls}>
                <TouchableOpacity style={styles.previewControlButton} onPress={switchCamera}>
                  <Text style={styles.previewControlText}>🔄</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.previewControlButton} onPress={stopPreview}>
                  <Text style={styles.previewControlText}>✕</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Stream Setup Form */}
          <View style={styles.form}>
            <TextInput
              style={styles.input}
              placeholder="Stream Title (required)"
              value={streamTitle}
              onChangeText={setStreamTitle}
              maxLength={STREAM_SETTINGS.maxTitleLength}
              editable={!isInitializing}
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Stream Description (optional)"
              value={streamDescription}
              onChangeText={setStreamDescription}
              multiline
              numberOfLines={3}
              maxLength={STREAM_SETTINGS.maxDescriptionLength}
              editable={!isInitializing}
            />

            <TouchableOpacity
              style={[
                styles.startButton,
                (!permissionsGranted || isInitializing || !streamTitle.trim()) && styles.startButtonDisabled
              ]}
              onPress={startStream}
              disabled={!permissionsGranted || isInitializing || !streamTitle.trim()}
            >
              {isInitializing ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.startButtonText}>
                  {!permissionsGranted ? 'Grant Permissions First' : 'Start Live Stream'}
                </Text>
              )}
            </TouchableOpacity>

            {!permissionsGranted && (
              <TouchableOpacity style={styles.permissionButton} onPress={requestPermissions}>
                <Text style={styles.permissionButtonText}>Grant Camera & Microphone Access</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      ) : (
        <View style={styles.liveContainer}>
          {/* Live Video Feed */}
          <View style={styles.liveVideoContainer}>
            <RtcLocalView.SurfaceView
              style={styles.liveVideo}
              channelId={channelNameRef.current}
              renderMode={1}
            />

            {/* Live Overlay */}
            <View style={styles.liveOverlay}>
              <View style={styles.liveHeader}>
                <View style={styles.liveBadge}>
                  <Text style={styles.liveText}>🔴 LIVE</Text>
                </View>
                <View style={styles.viewerBadge}>
                  <Text style={styles.viewerText}>👥 {viewerCount}</Text>
                </View>
              </View>

              <View style={styles.liveInfo}>
                <Text style={styles.liveTitle}>{streamTitle}</Text>
                {streamDescription ? (
                  <Text style={styles.liveDescription}>{streamDescription}</Text>
                ) : null}
              </View>
            </View>
          </View>

          {/* Live Stream Controls */}
          <View style={styles.liveControls}>
            <TouchableOpacity
              style={[styles.controlButton, isAudioMuted && styles.controlButtonMuted]}
              onPress={toggleAudio}
            >
              <Text style={styles.controlButtonText}>
                {isAudioMuted ? '🔇' : '🎤'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, isVideoMuted && styles.controlButtonMuted]}
              onPress={toggleVideo}
            >
              <Text style={styles.controlButtonText}>
                {isVideoMuted ? '📹' : '📷'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.controlButton} onPress={switchCamera}>
              <Text style={styles.controlButtonText}>🔄</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.endButton} onPress={stopStream}>
              <Text style={styles.endButtonText}>End Stream</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  permissionWarning: {
    fontSize: 14,
    color: '#ff4444',
    marginTop: 8,
    fontWeight: '500',
  },
  setupContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  previewArea: {
    flex: 1,
    backgroundColor: '#000',
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  localVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  previewPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  previewText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  previewSubtext: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  previewButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 20,
  },
  previewButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  previewControls: {
    position: 'absolute',
    top: 15,
    right: 15,
    flexDirection: 'row',
    gap: 10,
  },
  previewControlButton: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewControlText: {
    color: '#fff',
    fontSize: 18,
  },
  form: {
    padding: 20,
    backgroundColor: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  startButton: {
    backgroundColor: '#ff4444',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  startButtonDisabled: {
    backgroundColor: '#ccc',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 10,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  liveContainer: {
    flex: 1,
  },
  liveVideoContainer: {
    flex: 1,
    position: 'relative',
  },
  liveVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  liveOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: 20,
  },
  liveHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  liveBadge: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  liveText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  viewerBadge: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  viewerText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  liveInfo: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    padding: 15,
    borderRadius: 10,
  },
  liveTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  liveDescription: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 18,
  },
  liveControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.9)',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  controlButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonMuted: {
    backgroundColor: '#ff4444',
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 20,
  },
  endButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  endButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LiveStreamScreen;
