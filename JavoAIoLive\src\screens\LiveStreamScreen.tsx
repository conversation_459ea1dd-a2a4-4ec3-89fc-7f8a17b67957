import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  SafeAreaView,
  StatusBar,
} from 'react-native';

// Import Agora views with fallback
let RtcLocalView: any;
let RtcRemoteView: any;

try {
  const AgoraViews = require('react-native-agora');
  RtcLocalView = AgoraViews.RtcLocalView;
  RtcRemoteView = AgoraViews.RtcRemoteView;
} catch (error) {
  console.log('Agora views not available, using mock components');
  // Mock components for testing
  RtcLocalView = {
    SurfaceView: ({ style, children }: any) => (
      <View style={[style, { backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: '#fff', fontSize: 16 }}>📹 Mock Camera Feed</Text>
        <Text style={{ color: '#ccc', fontSize: 12, marginTop: 8 }}>Agora SDK will show real camera here</Text>
        {children}
      </View>
    ),
  };
  RtcRemoteView = {
    SurfaceView: ({ style, children }: any) => (
      <View style={[style, { backgroundColor: '#333' }]}>
        <Text style={{ color: '#fff' }}>Remote View</Text>
        {children}
      </View>
    ),
  };
}

// Import services and components
import AgoraService, { AgoraEventHandlers } from '../services/agora';
import PermissionsService from '../services/permissions';
import { STREAM_SETTINGS } from '../utils/constants';

// Import new components
import PermissionHandler from '../components/PermissionHandler';
import StreamSetup, { StreamConfig } from '../components/StreamSetup';
import StreamControls from '../components/StreamControls';
import StreamStats from '../components/StreamStats';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Screen modes
type ScreenMode = 'permissions' | 'setup' | 'streaming';

const LiveStreamScreen: React.FC = () => {
  // Screen state
  const [screenMode, setScreenMode] = useState<ScreenMode>('permissions');

  // Stream state
  const [isStreaming, setIsStreaming] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isPreviewActive, setIsPreviewActive] = useState(false);
  const [streamConfig, setStreamConfig] = useState<StreamConfig | null>(null);

  // Stream controls
  const [viewerCount, setViewerCount] = useState(0);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  // Stream statistics
  const [streamDuration, setStreamDuration] = useState(0);
  const [bitrate, setBitrate] = useState(0);
  const [networkQuality, setNetworkQuality] = useState<'excellent' | 'good' | 'poor' | 'bad'>('good');
  const [showStats, setShowStats] = useState(false);

  // Refs
  const channelNameRef = useRef<string>('');
  const uidRef = useRef<number>(0);
  const streamStartTimeRef = useRef<number>(0);

  useEffect(() => {
    checkInitialPermissions();
    return () => {
      cleanup();
    };
  }, []);

  // Check permissions on component mount
  const checkInitialPermissions = async () => {
    try {
      const permissions = await PermissionsService.checkAllPermissions();
      if (permissions.granted) {
        setPermissionsGranted(true);
        setScreenMode('setup');
      } else {
        setScreenMode('permissions');
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      setScreenMode('permissions');
    }
  };

  // Permission handlers
  const handlePermissionsGranted = () => {
    setPermissionsGranted(true);
    setScreenMode('setup');
  };

  const handlePermissionsDenied = () => {
    setPermissionsGranted(false);
    setScreenMode('permissions');
  };

  const initializeAgora = async () => {
    try {
      setIsInitializing(true);

      const eventHandlers: AgoraEventHandlers = {
        onJoinChannelSuccess: (channel, uid, elapsed) => {
          console.log('Successfully joined channel:', channel);
          setIsStreaming(true);
          setViewerCount(1); // Start with broadcaster
        },
        onUserJoined: (uid, elapsed) => {
          console.log('User joined:', uid);
          setViewerCount(prev => prev + 1);
        },
        onUserOffline: (uid, reason) => {
          console.log('User left:', uid);
          setViewerCount(prev => Math.max(0, prev - 1));
        },
        onError: (errorCode) => {
          console.error('Agora error:', errorCode);
          Alert.alert('Streaming Error', `Error code: ${errorCode}`);
        },
      };

      await AgoraService.initialize(eventHandlers);
      console.log('Agora initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Agora:', error);
      Alert.alert('Error', 'Failed to initialize streaming service');
    } finally {
      setIsInitializing(false);
    }
  };

  // Stream setup handler
  const handleStartStream = async (config: StreamConfig) => {
    try {
      setIsInitializing(true);
      setStreamConfig(config);

      // Initialize Agora if not already done
      if (!AgoraService.isInitialized()) {
        await initializeAgora();
      }

      // Generate channel name from stream title
      const channelName = config.title.toLowerCase().replace(/[^a-z0-9]/g, '') + '_' + Date.now();
      channelNameRef.current = channelName;

      // Start preview first
      await AgoraService.startPreview();
      setIsPreviewActive(true);

      // Join channel and start streaming
      const uid = Math.floor(Math.random() * 1000000);
      uidRef.current = uid;

      await AgoraService.joinChannel(channelName, uid);

      // Set stream start time for duration tracking
      streamStartTimeRef.current = Date.now();
      setStreamDuration(0);

      // Update bitrate based on quality setting
      const qualityBitrates = {
        low: 500,
        medium: 1000,
        high: 2000,
        ultra: 4000,
      };
      setBitrate(qualityBitrates[config.quality]);

      // Set streaming state to true to show controls
      setIsStreaming(true);
      setScreenMode('streaming');
      console.log('Stream started successfully');
    } catch (error) {
      console.error('Failed to start stream:', error);
      Alert.alert('Error', 'Failed to start live stream');
    } finally {
      setIsInitializing(false);
    }
  };

  // Stream control handlers
  const handleToggleMute = async () => {
    try {
      const newMutedState = !isAudioMuted;
      await AgoraService.muteLocalAudio(newMutedState);
      setIsAudioMuted(newMutedState);
    } catch (error) {
      console.error('Failed to toggle audio:', error);
    }
  };

  const handleToggleVideo = async () => {
    try {
      const newMutedState = !isVideoMuted;
      await AgoraService.muteLocalVideo(newMutedState);
      setIsVideoMuted(newMutedState);
    } catch (error) {
      console.error('Failed to toggle video:', error);
    }
  };

  const handleSwitchCamera = async () => {
    try {
      await AgoraService.switchCamera();
    } catch (error) {
      console.error('Failed to switch camera:', error);
    }
  };

  const handleEndStream = async () => {
    try {
      await AgoraService.leaveChannel();
      await AgoraService.stopPreview();

      setIsStreaming(false);
      setIsPreviewActive(false);
      setViewerCount(0);
      setStreamDuration(0);
      setScreenMode('setup');

      console.log('Live stream ended');
    } catch (error) {
      console.error('Failed to end stream:', error);
      Alert.alert('Error', 'Failed to end stream');
    }
  };

  // Toggle stats visibility
  const toggleStats = () => {
    setShowStats(!showStats);
  };

  const cleanup = async () => {
    try {
      if (isStreaming) {
        await handleEndStream();
      }
      await AgoraService.destroy();
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  };

  // Render different screens based on mode
  const renderContent = () => {
    switch (screenMode) {
      case 'permissions':
        return (
          <PermissionHandler
            onPermissionsGranted={handlePermissionsGranted}
            onPermissionsDenied={handlePermissionsDenied}
          />
        );

      case 'setup':
        return (
          <StreamSetup
            onStartStream={handleStartStream}
            isLoading={isInitializing}
          />
        );

      case 'streaming':
        return (
          <View style={styles.streamingContainer}>
            {/* Camera Feed */}
            <View style={styles.cameraContainer}>
              {isPreviewActive && AgoraService.isInitialized() ? (
                <RtcLocalView.SurfaceView
                  style={styles.localVideo}
                  channelId={channelNameRef.current}
                  renderMode={1}
                />
              ) : (
                <View style={styles.cameraPlaceholder}>
                  <Text style={styles.cameraPlaceholderText}>📹 Camera Feed</Text>
                  <Text style={styles.cameraPlaceholderSubtext}>
                    {!isPreviewActive ? 'Starting camera...' : 'Camera not available'}
                  </Text>
                </View>
              )}

              {/* Stats Toggle Button */}
              <TouchableOpacity
                style={styles.statsToggle}
                onPress={toggleStats}
              >
                <Text style={styles.statsToggleText}>📊</Text>
              </TouchableOpacity>
            </View>

            {/* Stream Controls */}
            <StreamControls
              isStreaming={isStreaming}
              isMuted={isAudioMuted}
              isVideoEnabled={!isVideoMuted}
              onToggleMute={handleToggleMute}
              onToggleVideo={handleToggleVideo}
              onSwitchCamera={handleSwitchCamera}
              onEndStream={handleEndStream}
              viewerCount={viewerCount}
            />

            {/* Stream Statistics */}
            <StreamStats
              isVisible={showStats}
              isStreaming={isStreaming}
              duration={streamDuration}
              bitrate={bitrate}
              resolution="1080x1920"
              fps={30}
              networkQuality={networkQuality}
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      {renderContent()}
    </SafeAreaView>
  );
};

export default LiveStreamScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  streamingContainer: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  localVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  cameraPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  cameraPlaceholderText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  cameraPlaceholderSubtext: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },
  statsToggle: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsToggleText: {
    fontSize: 20,
  },
});
