import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
} from 'react-native';

const LiveStreamScreen: React.FC = () => {
  const [streamTitle, setStreamTitle] = useState('');
  const [streamDescription, setStreamDescription] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);

  const startStream = () => {
    if (!streamTitle.trim()) {
      Alert.alert('Error', 'Please enter a stream title');
      return;
    }

    // TODO: Implement Agora live streaming
    setIsStreaming(true);
    Alert.alert('Info', 'Live streaming with Agora will be implemented next');
  };

  const stopStream = () => {
    setIsStreaming(false);
    Alert.alert('Info', 'Stream ended');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Go Live</Text>
        <Text style={styles.headerSubtitle}>Share your expertise</Text>
      </View>

      {!isStreaming ? (
        <View style={styles.setupContainer}>
          <View style={styles.previewArea}>
            <Text style={styles.previewText}>Camera Preview</Text>
            <Text style={styles.previewSubtext}>Agora camera preview will appear here</Text>
          </View>

          <View style={styles.form}>
            <TextInput
              style={styles.input}
              placeholder="Stream Title"
              value={streamTitle}
              onChangeText={setStreamTitle}
              maxLength={100}
            />
            
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Stream Description"
              value={streamDescription}
              onChangeText={setStreamDescription}
              multiline
              numberOfLines={3}
              maxLength={500}
            />

            <TouchableOpacity style={styles.startButton} onPress={startStream}>
              <Text style={styles.startButtonText}>Start Live Stream</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.liveContainer}>
          <View style={styles.livePreview}>
            <Text style={styles.liveText}>🔴 LIVE</Text>
            <Text style={styles.liveTitle}>{streamTitle}</Text>
          </View>

          <View style={styles.liveControls}>
            <TouchableOpacity style={styles.controlButton}>
              <Text style={styles.controlButtonText}>Mute</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.controlButton}>
              <Text style={styles.controlButtonText}>Camera</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.endButton} onPress={stopStream}>
              <Text style={styles.endButtonText}>End Stream</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  setupContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  previewArea: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
    borderRadius: 12,
  },
  previewText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  previewSubtext: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 8,
  },
  form: {
    padding: 20,
    backgroundColor: '#fff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  startButton: {
    backgroundColor: '#ff4444',
    borderRadius: 8,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  liveContainer: {
    flex: 1,
  },
  livePreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  liveText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff4444',
    marginBottom: 10,
  },
  liveTitle: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
  },
  liveControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  controlButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  endButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  endButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LiveStreamScreen;
