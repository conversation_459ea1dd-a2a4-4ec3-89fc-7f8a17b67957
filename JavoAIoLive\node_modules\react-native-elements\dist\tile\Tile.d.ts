import React from 'react';
import { TouchableOpacityProps, TouchableNativeFeedbackProps, ViewStyle, StyleProp, TextStyle, ImageProps, ImageSourcePropType } from 'react-native';
import { IconObject, IconProps } from '../icons/Icon';
import { RneFunctionComponent } from '../helpers';
export declare type TileProps = TouchableOpacityProps & TouchableNativeFeedbackProps & {
    title?: string;
    icon?: IconObject & IconProps;
    caption?: React.ReactNode;
    imageSrc?: ImageSourcePropType | string | number;
    activeOpacity?: number;
    containerStyle?: StyleProp<ViewStyle>;
    imageContainerStyle?: StyleProp<ViewStyle>;
    iconContainerStyle?: StyleProp<ViewStyle>;
    overlayContainerStyle?: StyleProp<ViewStyle>;
    titleStyle?: StyleProp<TextStyle>;
    captionStyle?: StyleProp<TextStyle>;
    width?: number;
    height?: number;
    featured?: boolean;
    contentContainerStyle?: StyleProp<ViewStyle>;
    titleNumberOfLines?: number;
    imageProps?: Partial<ImageProps>;
    ImageComponent?: typeof React.Component;
};
declare const Tile: RneFunctionComponent<TileProps>;
export { Tile };
declare const _default: React.FunctionComponent<Omit<TouchableOpacityProps & TouchableNativeFeedbackProps & {
    title?: string;
    icon?: IconObject & import("react-native-vector-icons/Icon").IconButtonProps & {
        type?: string;
        Component?: typeof React.Component;
        reverse?: boolean;
        raised?: boolean;
        containerStyle?: StyleProp<ViewStyle>;
        iconProps?: import("react-native-vector-icons/Icon").IconProps;
        reverseColor?: string;
        disabled?: boolean;
        disabledStyle?: StyleProp<ViewStyle>;
        solid?: boolean;
        brand?: boolean;
    };
    caption?: React.ReactNode;
    imageSrc?: string | ImageSourcePropType;
    activeOpacity?: number;
    containerStyle?: StyleProp<ViewStyle>;
    imageContainerStyle?: StyleProp<ViewStyle>;
    iconContainerStyle?: StyleProp<ViewStyle>;
    overlayContainerStyle?: StyleProp<ViewStyle>;
    titleStyle?: StyleProp<TextStyle>;
    captionStyle?: StyleProp<TextStyle>;
    width?: number;
    height?: number;
    featured?: boolean;
    contentContainerStyle?: StyleProp<ViewStyle>;
    titleNumberOfLines?: number;
    imageProps?: Partial<ImageProps>;
    ImageComponent?: typeof React.Component;
} & Partial<import("../config").ThemeProps<TileProps>>, keyof import("../config").ThemeProps<T>>> | React.ForwardRefExoticComponent<TouchableOpacityProps & TouchableNativeFeedbackProps & {
    title?: string;
    icon?: IconObject & import("react-native-vector-icons/Icon").IconButtonProps & {
        type?: string;
        Component?: typeof React.Component;
        reverse?: boolean;
        raised?: boolean;
        containerStyle?: StyleProp<ViewStyle>;
        iconProps?: import("react-native-vector-icons/Icon").IconProps;
        reverseColor?: string;
        disabled?: boolean;
        disabledStyle?: StyleProp<ViewStyle>;
        solid?: boolean;
        brand?: boolean;
    };
    caption?: React.ReactNode;
    imageSrc?: string | ImageSourcePropType;
    activeOpacity?: number;
    containerStyle?: StyleProp<ViewStyle>;
    imageContainerStyle?: StyleProp<ViewStyle>;
    iconContainerStyle?: StyleProp<ViewStyle>;
    overlayContainerStyle?: StyleProp<ViewStyle>;
    titleStyle?: StyleProp<TextStyle>;
    captionStyle?: StyleProp<TextStyle>;
    width?: number;
    height?: number;
    featured?: boolean;
    contentContainerStyle?: StyleProp<ViewStyle>;
    titleNumberOfLines?: number;
    imageProps?: Partial<ImageProps>;
    ImageComponent?: typeof React.Component;
} & Partial<import("../config").ThemeProps<TileProps>>>;
export default _default;
