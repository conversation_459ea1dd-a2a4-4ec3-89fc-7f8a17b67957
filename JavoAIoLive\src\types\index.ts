// User types
export interface User {
  uid: string;
  email: string;
  username: string;
  profilePicture?: string;
  createdAt: Date;
}

// Stream types
export interface LiveStream {
  id: string;
  title: string;
  description: string;
  broadcasterUid: string;
  broadcasterName: string;
  channelName: string;
  isActive: boolean;
  viewerCount: number;
  startTime: Date;
  endTime?: Date;
  thumbnailUrl?: string;
}

// Chat types
export interface ChatMessage {
  id: string;
  streamId: string;
  userId: string;
  username: string;
  message: string;
  timestamp: Date;
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Home: undefined;
  LiveStream: { streamId?: string };
  ViewStream: { streamId: string };
  Profile: undefined;
};

// Agora types
export interface AgoraConfig {
  appId: string;
  token?: string;
  channelName: string;
  uid: number;
}

// Stream status
export enum StreamStatus {
  IDLE = 'idle',
  STARTING = 'starting',
  LIVE = 'live',
  ENDING = 'ending',
  ENDED = 'ended',
}
